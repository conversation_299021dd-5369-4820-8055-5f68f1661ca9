import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:venvi/constants/rules.dart';
import 'package:venvi/constants/time_constants.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/custom_classes/date_fields.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/event_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/event_model.dart';
import 'package:venvi/router.dart';
import 'package:venvi/screens/bottom_nav/root_tab_tap_view_model.dart';
import 'package:venvi/screens/schedule/schedule_state.dart';
import 'package:venvi/screens/schedule/schedule_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/agenda_group.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/minute_trigger.dart';
import 'package:venvi/widgets/restricted_view.dart';

class ScheduleScreen extends StatelessWidget {
  final bool isFavoritesTab;

  const ScheduleScreen({super.key, required this.isFavoritesTab});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: GlassAppBar(
        isRootScreen: true,
        actions: [
          if (!isFavoritesTab)
            RestrictedView(
              rule: Rules.editEvents,
              child: IconButton(
                tooltip: 'Create Event',
                icon: const Icon(Icons.add),
                onPressed: () => context.go('${context.read<ConData>().conPath}/schedule/create'),
              ),
            ),
          IconButton(
            tooltip: 'Search',
            icon: const Icon(Icons.search),
            onPressed: () => AppRouter.pushNamedRoute(context, 'search'),
          ),
        ],
      ),
      body: BlocProvider(
        create: (context) => ScheduleViewModel(
          context.read<ConData>(),
          context.read<UserViewModel>(),
          context.read<EventViewModel>(),
          context.read<ConViewModel>(),
          isFavoritesTab,
        ),
        child: BlocBuilder<ScheduleViewModel, ScheduleState>(
          builder: (context, state) => MinuteTrigger(
            onTrigger: () => context.read<ScheduleViewModel>().updateTodayIndex(),
            child: Column(
              children: [
                Expanded(
                  child: PageView.builder(
                    key: ValueKey('${context.read<ConData>().conWidgetKey}-SchedulePageView-$isFavoritesTab'),
                    controller: context.read<ScheduleViewModel>().pageController,
                    onPageChanged: (index) => context.read<ScheduleViewModel>().setSelectedDayIndex(index, false),
                    itemCount: state.conDays.length,
                    itemBuilder: (context, index) => _SchedulePage(
                      isFavoritesTab: isFavoritesTab,
                      index: index,
                      day: state.conDays[index],
                      events: state.events,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(
                    AppTheme.screenPadding,
                    AppTheme.widgetPaddingSmall,
                    AppTheme.screenPadding,
                    0,
                  ),
                  child: ContentArea(
                    padding: const EdgeInsets.symmetric(horizontal: AppTheme.widgetPaddingSmall),
                    child: Wrap(
                      alignment: WrapAlignment.spaceEvenly,
                      children: [
                        for (var i = 0; i < state.conDays.length; i++)
                          ClipRRect(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadius),
                            child: InkWell(
                              borderRadius: BorderRadius.circular(AppTheme.borderRadius),
                              onTap: () => context.read<ScheduleViewModel>().setSelectedDayIndex(i, true),
                              child: Container(
                                color: i == state.selectedDayIndex
                                    ? Theme.of(context).colorScheme.secondary
                                    : Colors.transparent,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppTheme.widgetPadding,
                                  vertical: AppTheme.widgetPaddingSmall,
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      DateFormat.E().format(state.conDays[i].toDateTime()),
                                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                        color: i == state.selectedDayIndex
                                            ? Theme.of(context).colorScheme.onSecondary
                                            : Theme.of(context).colorScheme.onPrimaryContainer,
                                      ),
                                    ),
                                    Text(
                                      _formatDateWithSuffix(state.conDays[i].toDateTime()),
                                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                        color: i == state.selectedDayIndex
                                            ? Theme.of(context).colorScheme.onSecondary
                                            : Theme.of(context).colorScheme.onPrimaryContainer,
                                      ),
                                    ),
                                    if (state.todayDayIndex != null)
                                      Container(
                                        margin: const EdgeInsets.only(top: 2),
                                        height: 4,
                                        width: 4,
                                        decoration: BoxDecoration(
                                          color: i == state.todayDayIndex
                                              ? i == state.selectedDayIndex
                                                    ? Theme.of(context).colorScheme.onSecondary
                                                    : Theme.of(context).colorScheme.onPrimaryContainer
                                              : Colors.transparent,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDateWithSuffix(DateTime date) {
    final day = DateFormat.d().format(date);
    final suffix = _getDaySuffix(int.parse(day));
    return '$day$suffix';
  }

  String _getDaySuffix(int day) {
    if (11 <= day && day <= 13) {
      return 'th';
    }
    switch (day % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }
}

class _SchedulePage extends StatefulWidget {
  final bool isFavoritesTab;
  final int index;
  final DateFields day;
  final Map<DateFields, Map<TimeOfDay, List<EventModel>>>? events;

  const _SchedulePage({required this.isFavoritesTab, required this.index, required this.day, required this.events});

  @override
  State<_SchedulePage> createState() => _SchedulePageState();
}

class _SchedulePageState extends State<_SchedulePage> with AutomaticKeepAliveClientMixin<_SchedulePage> {
  late final ScrollController scrollController;

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final currentDayMap = widget.events?[widget.day] ?? {};
    final currentDayTimes = currentDayMap.keys.toList();

    final nextDayDate = widget.day.toDateTime().add(const Duration(days: 1));
    final nextDayMap = Map<TimeOfDay, List<EventModel>>.from(
      widget.events?[DateFields(year: nextDayDate.year, month: nextDayDate.month, day: nextDayDate.day)] ?? {},
    );
    nextDayMap.removeWhere((key, value) => key.hour > TimeConstants.latestTimeOfDayHourOverlap);
    final nextDayTimes = nextDayMap.keys.toList();
    final count = currentDayTimes.length + nextDayTimes.length;

    if (count == 0) {
      if (widget.isFavoritesTab) {
        return Center(
          child: ContentArea(
            fillWidth: false,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Favorites', style: Theme.of(context).textTheme.titleMedium),
                const SizedBox(height: AppTheme.widgetPaddingVerySmall),
                Text('Build your personal schedule', style: Theme.of(context).textTheme.bodyMedium),
                const SizedBox(height: AppTheme.widgetPaddingSmall),
                ElevatedButton.icon(
                  onPressed: () {
                    FocusManager.instance.primaryFocus?.unfocus();
                    context.go('${context.read<ConData>().conPath}/schedule');
                  },
                  icon: const Icon(Icons.calendar_today),
                  label: const Text('See Schedule'),
                ),
              ],
            ),
          ),
        );
      } else {
        return const Center(child: ContentArea(fillWidth: false, child: Text('No events this day')));
      }
    }

    return BlocListener<RootTabTapViewModel, BottomTab?>(
      listenWhen: (previous, current) =>
          context.read<ScheduleViewModel>().state.selectedDayIndex == widget.index &&
          current == (widget.isFavoritesTab ? BottomTab.favorites : BottomTab.schedule),
      listener: (context, state) {
        if (scrollController.hasClients) {
          scrollController.animateTo(0, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
        }
        context.read<RootTabTapViewModel>().clearTab();
      },
      child: ListView.separated(
        controller: scrollController,
        padding: const EdgeInsets.all(AppTheme.screenPadding),
        itemCount: count,
        separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
        itemBuilder: (context, index) {
          if (index < currentDayTimes.length) {
            final time = currentDayTimes[index];
            return AgendaGroup(title: time.format(context), events: currentDayMap[time]!);
          } else {
            index -= currentDayTimes.length;
            final time = nextDayTimes[index];
            return AgendaGroup(title: time.format(context), events: nextDayMap[time]!);
          }
        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
