import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:venvi/constants/event_type.dart';
import 'package:venvi/constants/image_type.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/custom_classes/date_fields.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/event_view_model.dart';
import 'package:venvi/models/event_model.dart';
import 'package:venvi/models/event_participant_model.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/venue_snippet_model.dart';
import 'package:venvi/repositories/event_repository.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/screens/edit_events/edit_event_state.dart';
import 'package:venvi/utils/time_utils.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class EditEventViewModel extends EditorScaffoldViewModel<EditEventState> {
  final ConData _conData;
  final EventViewModel _eventViewModel;
  final EventRepository _eventRepository;
  final ImageRepository _imageRepository;
  final ConViewModel _conViewModel;

  EditEventViewModel(
    super.initialState,
    this._conData,
    this._eventViewModel,
    this._eventRepository,
    this._imageRepository,
    this._conViewModel,
  );

  @override
  bool checkChanges(EditEventState initialState, EditEventState currentState) {
    return initialState.eventModel != currentState.eventModel ||
        currentState.primaryImageOverrideFile != null ||
        currentState.galleryImageFiles?.isNotEmpty == true;
  }

  @override
  Future<EditEventState> applyChanges(EditEventState initialState, EditEventState state) async {
    EventModel model = state.eventModel;

    final errors = <String>[];
    if (model.title?.isNotEmpty != true) {
      errors.add('Title cannot be empty');
    }
    if (model.startTime == null) {
      errors.add('Start date and time cannot be empty');
    }
    if (model.endTime == null) {
      errors.add('End date and time cannot be empty');
    }
    if (model.startTime != null && model.endTime != null) {
      if (model.endTime!.millisecondsSinceEpoch < model.startTime!.millisecondsSinceEpoch) {
        errors.add('End date and time is before start date and time');
      }
      final conDays = _conViewModel.getConDays();
      if (model.startTime!.toDate().isBefore(conDays.first.toDateTime())) {
        errors.add('Start date and time is before con start date');
      }
      if (model.endTime!.toDate().isAfter(conDays.last.toDateTime().add(const Duration(days: 1)))) {
        errors.add('End date and time is after con end date');
      }
    }

    if (errors.isNotEmpty) {
      throw EditorScaffoldException(errors);
    }

    // Creates image model for primary image
    late final ImageModel? primaryImageUploadModel;
    if (state.primaryImageOverrideFile != null) {
      primaryImageUploadModel = _imageRepository.createImageModel(_conData, ImageType.eventPrimary);
      model = model.copyWith(
        primaryImageId: primaryImageUploadModel.id,
        imageData: _addImageData(primaryImageUploadModel),
      );
    } else {
      primaryImageUploadModel = null;
    }

    // Creates image models for gallery images
    final List<ImageModel> galleryImageUploadModels = [];
    final List<XFile> galleryImageUploadFiles = [];
    for (final imageFile in state.galleryImageFiles ?? []) {
      final imageModel = _imageRepository.createImageModel(_conData, ImageType.eventGallery);
      galleryImageUploadModels.add(imageModel);
      galleryImageUploadFiles.add(imageFile);
    }
    model = model.copyWith(
      galleryImageIds: [...?model.galleryImageIds, for (final image in galleryImageUploadModels) image.id!],
      imageData: {...?model.imageData, for (final image in galleryImageUploadModels) image.id!: image},
    );

    final eventId = model.id;
    if (eventId == null) {
      final uploadedId = await _eventRepository.createEvent(_conData, model);
      if (uploadedId == null) {
        errors.add('Failed to create event');
      }
    } else {
      final success = await _eventRepository.updateEvent(_conData, eventId, model);
      if (!success) {
        errors.add('Failed to update event');
      }
    }

    if (errors.isNotEmpty) {
      throw EditorScaffoldException(errors);
    }

    final List<Future<dynamic>> imageUploadFutures = [];
    if (primaryImageUploadModel != null && state.primaryImageOverrideFile != null) {
      imageUploadFutures.add(_imageRepository.uploadImage(primaryImageUploadModel, state.primaryImageOverrideFile!));
    }
    for (int i = 0; i < galleryImageUploadModels.length; i++) {
      imageUploadFutures.add(_imageRepository.uploadImage(galleryImageUploadModels[i], galleryImageUploadFiles[i]));
    }
    final imageUploadResults = await Future.wait(imageUploadFutures);
    if (imageUploadResults.contains(false) || imageUploadResults.contains(null)) {
      throw const EditorScaffoldException(['Failed to upload images']);
    }

    return state.copyWith(eventModel: model);
  }

  Future<bool> deleteEvent() async {
    final eventId = state.eventModel.id;
    if (eventId == null) {
      return true;
    } else {
      return await _eventRepository.deleteEvent(_conData, eventId);
    }
  }

  void toggleSpotlight() {
    final newEventModel = state.eventModel.copyWith(spotlight: !(state.eventModel.spotlight ?? false));
    emit(state.copyWith(eventModel: newEventModel));
  }

  void setTitle(String title) {
    final newEventModel = state.eventModel.copyWith(title: title.trim());
    emit(state.copyWith(eventModel: newEventModel));
  }

  void setDesc(String description) {
    final newEventModel = state.eventModel.copyWith(desc: description.trim());
    emit(state.copyWith(eventModel: newEventModel));
  }

  void setVenue(VenueSnippetModel? venue) {
    final newEventModel = state.eventModel.copyWith(venue: venue);
    emit(state.copyWith(eventModel: newEventModel, conflictEvents: _eventViewModel.checkEventConflicts(newEventModel)));
  }

  void setAdultOnly(bool? adultOnly) {
    final newEventModel = state.eventModel.copyWith(adultOnly: adultOnly);
    emit(state.copyWith(eventModel: newEventModel));
  }

  void setEventType(EventType? type) {
    final newEventModel = state.eventModel.copyWith(type: type);
    emit(state.copyWith(eventModel: newEventModel));
  }

  void setPrimaryImageFile(XFile image) {
    final newState = state.copyWith(
      eventModel: state.eventModel.copyWith(
        primaryImageId: null,
        imageData: _removeImageData(state.eventModel.primaryImageId),
      ),
      primaryImageOverrideFile: image,
    );
    emit(newState);
  }

  void setPrimaryImageReference(ImageModel image) {
    if (image.id == null) {
      return;
    }
    final newState = state.copyWith(
      eventModel: state.eventModel.copyWith(primaryImageId: image.id, imageData: _addImageData(image)),
      primaryImageOverrideFile: null,
    );
    emit(newState);
  }

  void removePrimaryImage() {
    final newState = state.copyWith(
      eventModel: state.eventModel.copyWith(
        primaryImageId: null,
        imageData: _removeImageData(state.eventModel.primaryImageId),
      ),
      primaryImageOverrideFile: null,
    );
    emit(newState);
  }

  void addGalleryImageFile(XFile image) {
    final newState = state.copyWith(galleryImageFiles: [...?state.galleryImageFiles, image]);
    emit(newState);
  }

  void removeGalleryImageFile(XFile image) {
    final newState = state.copyWith(
      galleryImageFiles: state.galleryImageFiles?.where((element) => element.path != image.path).toList(),
    );
    emit(newState);
  }

  void removeGalleryImageReference(String imageId) {
    final newModel = state.eventModel.copyWith(
      galleryImageIds: [...?state.eventModel.galleryImageIds]..remove(imageId),
      imageData: _removeImageData(imageId),
    );
    emit(state.copyWith(eventModel: newModel));
  }

  Map<String, ImageModel> _addImageData(ImageModel imageModel) {
    if (imageModel.id == null) {
      return state.eventModel.imageData ?? {};
    }
    final newImageData = Map<String, ImageModel>.from(state.eventModel.imageData ?? {});
    newImageData[imageModel.id!] = imageModel;
    return newImageData;
  }

  Map<String, ImageModel> _removeImageData(String? imageId) {
    final newImageData = Map<String, ImageModel>.from(state.eventModel.imageData ?? {});
    newImageData.remove(imageId);
    return newImageData;
  }

  void addTag(String tag) {
    final model = state.eventModel;
    final tags = model.tags ?? [];
    if (tags.length >= InputConstants.maxEventTags || tag.isEmpty || tags.contains(tag)) {
      return;
    }
    final newModel = model.copyWith(tags: [...tags, tag]);
    emit(state.copyWith(eventModel: newModel));
  }

  void removeTag(String tag) {
    final model = state.eventModel;
    final tags = model.tags ?? [];
    final newModel = model.copyWith(tags: tags.where((element) => element != tag).toList());
    emit(state.copyWith(eventModel: newModel));
  }

  void setAdmin(ParticipantModel participantModel) {
    final newModel = state.eventModel.copyWith(adminId: participantModel.id);
    emit(state.copyWith(eventModel: newModel));
  }

  void removeAdmin() {
    final newModel = state.eventModel.copyWith(adminId: null);
    emit(state.copyWith(eventModel: newModel));
  }

  void addParticipant(ParticipantModel participantModel, String roleId) {
    final participantId = participantModel.id;
    if (participantId == null) {
      return;
    }
    var model = state.eventModel;
    final List<EventParticipantModel> participants = model.participants?.toList() ?? [];
    if (participants.any((element) => element.participantId == participantId)) {
      return;
    }
    participants.add(EventParticipantModel(participantId: participantId, roleId: roleId));
    model = model.copyWith(participants: participants);
    emit(state.copyWith(eventModel: model));
  }

  void removeParticipant(String participantId) {
    var model = state.eventModel;
    final List<EventParticipantModel> participants = model.participants?.toList() ?? [];
    participants.removeWhere((element) => element.participantId == participantId);
    model = model.copyWith(participants: participants);
    emit(state.copyWith(eventModel: model));
  }

  void setStartDay(DateFields date) {
    var newState = state.copyWith(
      startDay: DateFields(year: date.year, month: date.month, day: date.day),
    );

    if (state.endDay == null) {
      newState = newState.copyWith(
        endDay: DateFields(year: date.year, month: date.month, day: date.day),
      );
      newState = _updateModelTimestamps(newState);
    } else {
      newState = _updateModelTimestamps(newState);
      newState = _moveEndTimesBasedOnNewTimestamps(newState);
    }

    emit(newState);
  }

  void setStartTime(TimeOfDay startTime) {
    var newState = state.copyWith(startTime: startTime);

    if (state.endTime == null) {
      newState = newState.copyWith(endTime: _addDuration(startTime, InputConstants.defaultEventDuration));
      newState = _updateModelTimestamps(newState);
    } else {
      newState = _updateModelTimestamps(newState);
      newState = _moveEndTimesBasedOnNewTimestamps(newState);
    }

    emit(newState);
  }

  void setEndDay(DateFields date) {
    var newState = state.copyWith(
      endDay: DateFields(year: date.year, month: date.month, day: date.day),
    );

    newState = _updateModelTimestamps(newState);
    emit(newState);
  }

  void setEndTime(TimeOfDay endTime) {
    var newState = state.copyWith(endTime: endTime);

    newState = _updateModelTimestamps(newState);
    emit(newState);
  }

  TimeOfDay _addDuration(TimeOfDay time, Duration duration) {
    final minutes = duration.inMinutes;
    if (minutes == 0) {
      return time;
    } else {
      int minutesOfDay = time.hour * 60 + time.minute;
      int newMinutesOfDay = ((minutes % 1440) + minutesOfDay + 1440) % 1440;
      if (minutesOfDay == newMinutesOfDay) {
        return time;
      } else {
        int newHour = newMinutesOfDay ~/ 60;
        int newMinute = newMinutesOfDay % 60;
        return TimeOfDay(hour: newHour, minute: newMinute);
      }
    }
  }

  EditEventState _moveEndTimesBasedOnNewTimestamps(EditEventState newState) {
    final oldStartTime = state.eventModel.startTime;
    final oldEndTime = state.eventModel.endTime;
    final newStartTime = newState.eventModel.startTime;

    if (oldStartTime == null || oldEndTime == null || newStartTime == null || oldStartTime == newStartTime) {
      return newState;
    }

    final oldDuration = oldEndTime.toDate().difference(oldStartTime.toDate());
    final newEndDateTime = newStartTime.toDate().add(oldDuration);
    final updatedEndDay = DateFields(year: newEndDateTime.year, month: newEndDateTime.month, day: newEndDateTime.day);
    final updatedEndTime = TimeOfDay(hour: newEndDateTime.hour, minute: newEndDateTime.minute);

    return newState.copyWith(
      endDay: updatedEndDay,
      endTime: updatedEndTime,
      eventModel: newState.eventModel.copyWith(endTime: Timestamp.fromDate(newEndDateTime)),
    );
  }

  EditEventState _updateModelTimestamps(EditEventState newState) {
    final model = newState.eventModel;

    var modelStartTime = model.startTime;
    var modelEndTime = model.endTime;

    final stateStartDay = newState.startDay;
    final stateEndDay = newState.endDay;
    final stateStartTime = newState.startTime;
    final stateEndTime = newState.endTime;

    if (stateStartDay != null && stateStartTime != null) {
      final tzStartTime = TimeUtils.createConDateTime(
        _conViewModel.state?.location,
        year: stateStartDay.year,
        month: stateStartDay.month,
        day: stateStartDay.day,
        hour: stateStartTime.hour,
        minute: stateStartTime.minute,
      );
      modelStartTime = tzStartTime != null ? Timestamp.fromDate(tzStartTime) : null;
    } else {
      modelStartTime = null;
    }

    if (stateEndDay != null && stateEndTime != null) {
      final tzEndTime = TimeUtils.createConDateTime(
        _conViewModel.state?.location,
        year: stateEndDay.year,
        month: stateEndDay.month,
        day: stateEndDay.day,
        hour: stateEndTime.hour,
        minute: stateEndTime.minute,
      );
      modelEndTime = tzEndTime != null ? Timestamp.fromDate(tzEndTime) : null;
    } else {
      modelEndTime = null;
    }
    final newEventModel = model.copyWith(startTime: modelStartTime, endTime: modelEndTime);

    return newState.copyWith(
      eventModel: newEventModel,
      conflictEvents: _eventViewModel.checkEventConflicts(newEventModel),
    );
  }
}
