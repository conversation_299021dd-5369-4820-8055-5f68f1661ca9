import 'package:image_picker/image_picker.dart';
import 'package:venvi/constants/image_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class EditManualParticipantState {
  final ParticipantModel participantModel;
  final XFile? profilePhotoOverrideFile;

  EditManualParticipantState({
    required this.participantModel,
    this.profilePhotoOverrideFile,
  });

  EditManualParticipantState copyWith({
    ParticipantModel? participantModel,
    XFile? profilePhotoOverrideFile,
  }) {
    return EditManualParticipantState(
      participantModel: participantModel ?? this.participantModel,
      profilePhotoOverrideFile: profilePhotoOverrideFile,
    );
  }
}

class EditManualParticipantViewModel extends EditorScaffoldViewModel<EditManualParticipantState> {
  final ConData conData;
  final ParticipantRepository _participantRepository;
  final ImageRepository _imageRepository;
  final String? participantId;
  final bool isNewParticipant;

  EditManualParticipantViewModel(
    ParticipantModel initialParticipant,
    this.conData,
    this._participantRepository,
    this._imageRepository,
    this.participantId,
    this.isNewParticipant,
  ) : super(EditManualParticipantState(participantModel: initialParticipant));

  @override
  bool checkChanges(EditManualParticipantState initialState, EditManualParticipantState currentState) {
    return currentState.participantModel != initialState.participantModel ||
        currentState.profilePhotoOverrideFile != initialState.profilePhotoOverrideFile;
  }

  Map<String, dynamic>? _addImageData(ImageModel? image) {
    if (image?.id == null) {
      return state.participantModel.imageData;
    }
    return {...?state.participantModel.imageData, image!.id!: image.toJson()};
  }

  Map<String, dynamic>? _removeImageData(String? imageId) {
    if (imageId == null || state.participantModel.imageData == null) {
      return state.participantModel.imageData;
    }
    final newImageData = Map<String, dynamic>.from(state.participantModel.imageData!);
    newImageData.remove(imageId);
    return newImageData.isEmpty ? null : newImageData;
  }

  @override
  Future<EditManualParticipantState> applyChanges(EditManualParticipantState initialState, EditManualParticipantState state) async {
    final errors = <String>[];

    if (state.participantModel.displayName?.isNotEmpty != true) {
      errors.add('Display name is required');
    }

    if (errors.isNotEmpty) {
      throw EditorScaffoldException(errors);
    }

    ParticipantModel model = state.participantModel;

    // Handle profile photo upload
    late final ImageModel? profilePhotoUploadModel;
    if (state.profilePhotoOverrideFile != null) {
      profilePhotoUploadModel = _imageRepository.createImageModel(conData, ImageType.profilePhoto);
      model = model.copyWith(
        profilePhotoId: profilePhotoUploadModel.id,
        imageData: _addImageData(profilePhotoUploadModel),
      );
    } else {
      profilePhotoUploadModel = null;
    }

    if (isNewParticipant) {
      final docId = await _participantRepository.createManualParticipant(conData, model);
      if (docId != null) {
        model = model.copyWith(id: docId);
        // Upload profile photo after participant is created
        if (profilePhotoUploadModel != null && state.profilePhotoOverrideFile != null) {
          await _imageRepository.uploadImage(profilePhotoUploadModel, state.profilePhotoOverrideFile!);
        }
        return state.copyWith(participantModel: model);
      } else {
        throw const EditorScaffoldException(['Failed to create profile']);
      }
    } else {
      final success = await _participantRepository.updateManualParticipant(conData, participantId!, model);
      if (success) {
        // Upload profile photo after participant is updated
        if (profilePhotoUploadModel != null && state.profilePhotoOverrideFile != null) {
          await _imageRepository.uploadImage(profilePhotoUploadModel, state.profilePhotoOverrideFile!);
        }
        return state.copyWith(participantModel: model);
      } else {
        throw const EditorScaffoldException(['Failed to update profile']);
      }
    }
  }

  void setDisplayName(String displayName) {
    emit(state.copyWith(
      participantModel: state.participantModel.copyWith(
        displayName: displayName.trim(),
      ),
    ));
  }

  void setConHeadline(String conHeadline) {
    emit(state.copyWith(
      participantModel: state.participantModel.copyWith(
        conHeadline: conHeadline.trim(),
      ),
    ));
  }

  void setConBio(String conBio) {
    emit(state.copyWith(
      participantModel: state.participantModel.copyWith(
        conBio: conBio.trim(),
      ),
    ));
  }

  void setProfilePhotoFile(XFile image) {
    final newState = state.copyWith(
      participantModel: state.participantModel.copyWith(
        profilePhotoId: null,
        imageData: _removeImageData(state.participantModel.profilePhotoId),
      ),
      profilePhotoOverrideFile: image,
    );
    emit(newState);
  }

  void removeProfilePhoto() {
    final newState = state.copyWith(
      participantModel: state.participantModel.copyWith(
        profilePhotoId: null,
        imageData: _removeImageData(state.participantModel.profilePhotoId),
      ),
      profilePhotoOverrideFile: null,
    );
    emit(newState);
  }
}
