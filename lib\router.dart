import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/screen_profile/profile_screen.dart';
import 'package:venvi/screens/admin_tools/admin_screen.dart';
import 'package:venvi/screens/admin_tools/analytics_screen.dart';
import 'package:venvi/screens/admin_tools/announcement_manager_screen.dart';
import 'package:venvi/screens/admin_tools/con_manager_screen.dart';
import 'package:venvi/screens/admin_tools/conflict_detector_screen.dart';
import 'package:venvi/screens/admin_tools/event_properties_manager_screen.dart';
import 'package:venvi/screens/admin_tools/home_screen_gallery_screen.dart';
import 'package:venvi/screens/admin_tools/org_manager_screen.dart';
import 'package:venvi/screens/admin_tools/permissions_manager_screen.dart';
import 'package:venvi/screens/admin_tools/reusable_event_image_library_screen.dart';
import 'package:venvi/screens/bottom_nav/bottom_nav_screen.dart';
import 'package:venvi/screens/con_search/con_search_screen.dart';
import 'package:venvi/screens/con_search/con_search_shell_screen.dart';
import 'package:venvi/screens/create_con/create_con_screen.dart';
import 'package:venvi/screens/delete_account/delete_account_screen.dart';
import 'package:venvi/screens/edit_events/edit_event_screen.dart';
import 'package:venvi/screens/error/error_screen.dart';
import 'package:venvi/screens/event_details/event_details_screen.dart';
import 'package:venvi/screens/general_content/general_content_page_editor_screen.dart';
import 'package:venvi/screens/general_content/general_content_reorder_sections_screen.dart';
import 'package:venvi/screens/general_content/general_content_screen.dart';
import 'package:venvi/screens/home/<USER>';
import 'package:venvi/screens/import_export_schedule/import_export_screen.dart';
import 'package:venvi/screens/map/map_editor_screen.dart';
import 'package:venvi/screens/map/map_screen.dart';
import 'package:venvi/screens/map/venue_editor_screen.dart';
import 'package:venvi/screens/org_con_list/org_con_list_screen.dart';
import 'package:venvi/screens/roles_and_participants/participant_manager_screen.dart';
import 'package:venvi/screens/roles_and_participants/role_manager_screen.dart';
import 'package:venvi/screens/roles_and_participants/role_participants_screen.dart';
import 'package:venvi/screens/roles_and_participants/roles_list_admin_screen.dart';
import 'package:venvi/screens/roles_and_participants/roles_reorder_screen.dart';
import 'package:venvi/screens/schedule/schedule_screen.dart';
import 'package:venvi/screens/schedule/schedule_search_screen.dart';
import 'package:venvi/screens/settings/settings_screen.dart';
import 'package:venvi/splash_screen.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/logger.dart';

abstract class AppRouter {
  static const String _orgParamName = 'orgId';
  static const String _conParamName = 'conId';
  static const String _eventParamName = 'eventId';
  static const String _profileParamName = 'profileId';
  static const String _roleParamName = 'roleId';
  static const String _generalContentParamName = 'generalContentId';

  static String _lastValidOrgId = '';
  static String _lastValidConId = '';

  static final router = GoRouter(
    initialLocation: '/',
    observers: [if (kReleaseMode) SentryNavigatorObserver()],
    redirect: (context, state) {
      FirebaseAnalytics.instance.logScreenView(screenName: state.fullPath);
      return null;
    },
    routes: [
      GoRoute(path: '/', pageBuilder: (context, state) => _pageBuilder(state, const SplashScreen())),
      ShellRoute(
        builder: (context, state, child) => ConSearchShellScreen(child: child),
        routes: [
          GoRoute(
            path: '/home',
            pageBuilder: (context, state) => _pageBuilder(state, const ConSearchScreen()),
            routes: [
              GoRoute(
                path: 'settings',
                pageBuilder: (context, state) => _pageBuilder(state, const SettingsScreen(conData: null)),
                routes: [
                  GoRoute(
                    path: 'delete-account',
                    pageBuilder: (context, state) => _pageBuilder(state, const DeleteAccountScreen()),
                  ),
                ],
              ),
              GoRoute(
                path: '/create-con',
                pageBuilder: (context, state) => _pageBuilder(state, CreateConScreen(orgId: null)),
              ),
            ],
          ),
        ],
      ),
      GoRoute(
        path: '/:$_orgParamName/:$_conParamName',
        redirect: (context, state) {
          final newOrgId = state.pathParameters[_orgParamName];
          final newConId = state.pathParameters[_conParamName];

          // Updates last valid IDs
          if (newOrgId != null && newOrgId != ':$_orgParamName') {
            _lastValidOrgId = newOrgId;
          }
          if (newConId != null && newConId != ':$_conParamName') {
            _lastValidConId = newConId;
          }

          final originalPath = state.fullPath ?? '/';

          final isConRoot = Uri.parse(originalPath).pathSegments.last == ':$_conParamName';
          if (isConRoot) {
            return '/$_lastValidOrgId/$_lastValidConId/home';
          }

          if (newOrgId == ':$_orgParamName' || newConId == ':$_conParamName') {
            Logger.error(message: 'Path parameter restored, GoRouter still broken');
            return originalPath
                .replaceAll(':$_orgParamName', _lastValidOrgId)
                .replaceAll(':$_conParamName', _lastValidConId);
          }
          return null;
        },
        routes: [
          StatefulShellRoute.indexedStack(
            pageBuilder: (context, state, navigationShell) => _pageBuilder(
              state,
              BottomNavScreen(
                // Needed to force reload if con changes
                key: ValueKey('${_lastValidOrgId}_$_lastValidConId'),
                orgId: _lastValidOrgId,
                conId: _lastValidConId,
                navigationShell: navigationShell,
              ),
            ),
            branches: [
              StatefulShellBranch(
                routes: [
                  GoRoute(
                    path: 'home',
                    pageBuilder: (context, state) => _pageBuilder(state, const HomeScreen()),
                    routes: [
                      GoRoute(
                        path: 'settings',
                        pageBuilder: (context, state) => _pageBuilder(
                          state,
                          SettingsScreen(
                            conData: ConData(
                              orgId: state.pathParameters[_orgParamName]!,
                              conId: state.pathParameters[_conParamName]!,
                            ),
                          ),
                        ),
                        routes: [
                          GoRoute(
                            path: 'delete-account',
                            pageBuilder: (context, state) => _pageBuilder(state, const DeleteAccountScreen()),
                          ),
                        ],
                      ),
                      GoRoute(
                        path: 'copy/:$_eventParamName',
                        pageBuilder: (context, state) => _pageBuilder(
                          state,
                          EditEventScreen(eventId: state.pathParameters[_eventParamName], isNewEvent: true),
                        ),
                      ),
                      GoRoute(
                        path: 'event/:$_eventParamName',
                        pageBuilder: (context, state) =>
                            _pageBuilder(state, EventDetailsScreen(eventId: state.pathParameters[_eventParamName])),
                        routes: [
                          GoRoute(
                            path: 'profile/:$_profileParamName',
                            pageBuilder: (context, state) => _pageBuilder(
                              state,
                              ProfileScreen(
                                isFullScreenDialog: false,
                                profileId: state.pathParameters[_profileParamName],
                              ),
                            ),
                          ),
                          GoRoute(
                            path: 'edit',
                            pageBuilder: (context, state) => _pageBuilder(
                              state,
                              EditEventScreen(eventId: state.pathParameters[_eventParamName], isNewEvent: false),
                            ),
                          ),
                        ],
                      ),
                      GoRoute(
                        path: 'role/:$_roleParamName',
                        pageBuilder: (context, state) =>
                            _pageBuilder(state, RoleParticipantsScreen(roleId: state.pathParameters[_roleParamName])),
                        routes: [
                          GoRoute(
                            path: 'profile/:$_profileParamName',
                            pageBuilder: (context, state) => _pageBuilder(
                              state,
                              ProfileScreen(
                                isFullScreenDialog: false,
                                profileId: state.pathParameters[_profileParamName],
                              ),
                            ),
                          ),
                        ],
                      ),
                      GoRoute(
                        path: 'general-content/:$_generalContentParamName',
                        pageBuilder: (context, state) => _pageBuilder(
                          state,
                          GeneralContentScreen(generalContentId: state.pathParameters[_generalContentParamName]),
                        ),
                        routes: [
                          GoRoute(
                            path: 'edit',
                            pageBuilder: (context, state) => _pageBuilder(
                              state,
                              GeneralContentPageEditorScreen(
                                generalContentId: state.pathParameters[_generalContentParamName],
                              ),
                            ),
                          ),
                        ],
                      ),
                      GoRoute(
                        path: 'con-list',
                        pageBuilder: (context, state) => _pageBuilder(state, const OrgConListScreen()),
                      ),
                    ],
                  ),
                ],
              ),
              StatefulShellBranch(
                routes: [
                  GoRoute(
                    path: 'favorites',
                    pageBuilder: (context, state) => _pageBuilder(state, const ScheduleScreen(isFavoritesTab: true)),
                    routes: [
                      GoRoute(
                        path: 'search',
                        pageBuilder: (context, state) => _pageBuilder(state, const ScheduleSearchScreen()),
                      ),
                      GoRoute(
                        path: 'copy/:$_eventParamName',
                        pageBuilder: (context, state) => _pageBuilder(
                          state,
                          EditEventScreen(eventId: state.pathParameters[_eventParamName], isNewEvent: true),
                        ),
                      ),
                      GoRoute(
                        path: 'event/:$_eventParamName',
                        pageBuilder: (context, state) =>
                            _pageBuilder(state, EventDetailsScreen(eventId: state.pathParameters[_eventParamName])),
                        routes: [
                          GoRoute(
                            path: 'profile/:$_profileParamName',
                            pageBuilder: (context, state) => _pageBuilder(
                              state,
                              ProfileScreen(
                                isFullScreenDialog: false,
                                profileId: state.pathParameters[_profileParamName],
                              ),
                            ),
                          ),
                          GoRoute(
                            path: 'edit',
                            pageBuilder: (context, state) => _pageBuilder(
                              state,
                              EditEventScreen(eventId: state.pathParameters[_eventParamName], isNewEvent: false),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
              StatefulShellBranch(
                routes: [
                  GoRoute(
                    path: 'schedule',
                    pageBuilder: (context, state) => _pageBuilder(state, const ScheduleScreen(isFavoritesTab: false)),
                    routes: [
                      GoRoute(
                        path: 'search',
                        pageBuilder: (context, state) => _pageBuilder(state, const ScheduleSearchScreen()),
                      ),
                      GoRoute(
                        path: 'create',
                        pageBuilder: (context, state) => _pageBuilder(state, const EditEventScreen(isNewEvent: true)),
                      ),
                      GoRoute(
                        path: 'copy/:$_eventParamName',
                        pageBuilder: (context, state) => _pageBuilder(
                          state,
                          EditEventScreen(eventId: state.pathParameters[_eventParamName], isNewEvent: true),
                        ),
                      ),
                      GoRoute(
                        path: 'event/:$_eventParamName',
                        pageBuilder: (context, state) =>
                            _pageBuilder(state, EventDetailsScreen(eventId: state.pathParameters[_eventParamName])),
                        routes: [
                          GoRoute(
                            path: 'profile/:$_profileParamName',
                            pageBuilder: (context, state) => _pageBuilder(
                              state,
                              ProfileScreen(
                                isFullScreenDialog: false,
                                profileId: state.pathParameters[_profileParamName],
                              ),
                            ),
                          ),
                          GoRoute(
                            path: 'edit',
                            pageBuilder: (context, state) => _pageBuilder(
                              state,
                              EditEventScreen(eventId: state.pathParameters[_eventParamName], isNewEvent: false),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
              StatefulShellBranch(
                routes: [GoRoute(path: 'map', pageBuilder: (context, state) => _pageBuilder(state, const MapScreen()))],
              ),
              StatefulShellBranch(
                routes: [
                  GoRoute(
                    path: 'admin',
                    pageBuilder: (context, state) => _pageBuilder(state, const AdminScreen()),
                    routes: [
                      GoRoute(
                        path: 'copy/:$_eventParamName',
                        pageBuilder: (context, state) => _pageBuilder(
                          state,
                          EditEventScreen(eventId: state.pathParameters[_eventParamName], isNewEvent: true),
                        ),
                      ),
                      GoRoute(
                        path: 'event/:$_eventParamName',
                        pageBuilder: (context, state) =>
                            _pageBuilder(state, EventDetailsScreen(eventId: state.pathParameters[_eventParamName])),
                        routes: [
                          GoRoute(
                            path: 'profile/:$_profileParamName',
                            pageBuilder: (context, state) => _pageBuilder(
                              state,
                              ProfileScreen(
                                isFullScreenDialog: false,
                                profileId: state.pathParameters[_profileParamName],
                              ),
                            ),
                          ),
                          GoRoute(
                            path: 'edit',
                            pageBuilder: (context, state) => _pageBuilder(
                              state,
                              EditEventScreen(eventId: state.pathParameters[_eventParamName], isNewEvent: false),
                            ),
                          ),
                        ],
                      ),
                      GoRoute(
                        path: 'announcement-manager',
                        pageBuilder: (context, state) => _pageBuilder(state, const AnnouncementManagerScreen()),
                      ),
                      GoRoute(
                        path: 'create-event',
                        pageBuilder: (context, state) =>
                            _pageBuilder(state, const EditEventScreen(isNewEvent: true, multiAdd: true)),
                      ),
                      GoRoute(
                        path: 'import-export',
                        pageBuilder: (context, state) => _pageBuilder(state, const ImportExportScreen()),
                      ),
                      GoRoute(
                        path: 'home-screen-gallery',
                        pageBuilder: (context, state) => _pageBuilder(state, const HomeScreenGalleryScreen()),
                      ),
                      GoRoute(
                        path: 'reusable-event-image-library',
                        pageBuilder: (context, state) => _pageBuilder(state, const ReusableEventImageLibraryScreen()),
                      ),
                      GoRoute(
                        path: 'conflict-detector',
                        pageBuilder: (context, state) => _pageBuilder(state, const ConflictDetectorScreen()),
                      ),
                      GoRoute(
                        path: 'roles',
                        pageBuilder: (context, state) => _pageBuilder(state, const RolesListAdminScreen()),
                        routes: [
                          GoRoute(
                            path: 'reorder',
                            pageBuilder: (context, state) => _pageBuilder(state, const RolesReorderScreen()),
                          ),
                          GoRoute(
                            path: 'participant-manager',
                            pageBuilder: (context, state) => _pageBuilder(state, const ParticipantManagerScreen()),
                            routes: [
                              GoRoute(
                                path: 'profile/:$_profileParamName',
                                pageBuilder: (context, state) => _pageBuilder(
                                  state,
                                  ProfileScreen(
                                    isFullScreenDialog: false,
                                    profileId: state.pathParameters[_profileParamName],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          GoRoute(
                            path: 'role-manager/:$_roleParamName',
                            pageBuilder: (context, state) =>
                                _pageBuilder(state, RoleManagerScreen(roleId: state.pathParameters[_roleParamName])),
                            routes: [
                              GoRoute(
                                path: 'profile/:$_profileParamName',
                                pageBuilder: (context, state) => _pageBuilder(
                                  state,
                                  ProfileScreen(
                                    isFullScreenDialog: false,
                                    profileId: state.pathParameters[_profileParamName],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      GoRoute(
                        path: 'event-properties-manager',
                        pageBuilder: (context, state) => _pageBuilder(state, const EventPropertiesManagerScreen()),
                      ),
                      GoRoute(
                        path: 'con-manager',
                        pageBuilder: (context, state) => _pageBuilder(state, const ConManagerScreen()),
                      ),
                      GoRoute(
                        path: 'venue-manager',
                        pageBuilder: (context, state) => _pageBuilder(state, const VenueEditorScreen()),
                      ),
                      GoRoute(
                        path: 'map-manager',
                        pageBuilder: (context, state) => _pageBuilder(state, const MapEditorScreen()),
                      ),
                      GoRoute(
                        path: 'create-general-content',
                        pageBuilder: (context, state) => _pageBuilder(state, const GeneralContentPageEditorScreen()),
                      ),
                      GoRoute(
                        path: 'reorder-general-content',
                        pageBuilder: (context, state) =>
                            _pageBuilder(state, const GeneralContentReorderSectionsScreen()),
                      ),
                      GoRoute(
                        path: 'analytics',
                        pageBuilder: (context, state) => _pageBuilder(state, const AnalyticsScreen()),
                      ),
                      GoRoute(
                        path: 'org-manager',
                        pageBuilder: (context, state) => _pageBuilder(state, const OrgManagerScreen()),
                        routes: [
                          GoRoute(
                            path: 'create-con',
                            pageBuilder: (context, state) =>
                                _pageBuilder(state, CreateConScreen(orgId: state.pathParameters[_orgParamName])),
                          ),
                        ],
                      ),
                      GoRoute(
                        path: 'permissions-manager',
                        pageBuilder: (context, state) => _pageBuilder(state, const PermissionsManagerScreen()),
                        routes: [
                          GoRoute(
                            path: 'profile/:$_profileParamName',
                            pageBuilder: (context, state) => _pageBuilder(
                              state,
                              ProfileScreen(
                                isFullScreenDialog: false,
                                profileId: state.pathParameters[_profileParamName],
                              ),
                            ),
                          ),
                        ],
                      ),
                      GoRoute(
                        path: 'con-list',
                        pageBuilder: (context, state) => _pageBuilder(state, const OrgConListScreen()),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => ErrorScreen(
      message: 'Page not found\n${state.uri}',
      buttonText: 'Go To Homepage',
      onTap: () => context.go('/home'),
    ),
  );

  static Future<T?> pushFullScreenDialog<T>(BuildContext context, Widget screen) async {
    FirebaseAnalytics.instance.logScreenView(screenName: screen.runtimeType.toString());
    return await Navigator.of(context).push<T?>(
      PageRouteBuilder(
        settings: RouteSettings(name: screen.toString()),
        fullscreenDialog: true,
        transitionsBuilder: _fullScreenDialogTransitionsBuilder,
        pageBuilder: (context, animation, secondaryAnimation) => SafeArea(
          child: Container(
            margin: const EdgeInsets.all(AppTheme.screenPadding),
            padding: const EdgeInsets.only(top: AppTheme.widgetPaddingVerySmall),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppTheme.borderRadius),
            ),
            child: screen,
          ),
        ),
      ),
    );
  }

  static void pushNamedRoute(BuildContext context, String path) {
    final currentPath = GoRouter.of(context).routeInformationProvider.value.uri.path;
    final separator = currentPath.endsWith('/') ? '' : '/';
    context.go('$currentPath$separator$path');
  }

  static void gotoNamedRouteInTab(BuildContext context, String path) {
    final currentPath = GoRouter.of(context).routeInformationProvider.value.uri.path;
    final tabPath = currentPath.split('/').sublist(0, 4).join('/');
    context.go('$tabPath/$path');
  }

  static Page _pageBuilder(GoRouterState state, Widget child) {
    return CustomTransitionPage(name: state.fullPath, transitionsBuilder: _glassTransitionsBuilder, child: child);
  }

  static Widget _glassTransitionsBuilder(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    final slideTween = Tween<Offset>(begin: const Offset(1, 0), end: const Offset(0, 0));

    final curvedAnimation = CurvedAnimation(parent: animation, curve: Curves.easeOutQuad);

    final secondaryFadeTween = Tween<double>(begin: 1, end: 0);

    return SlideTransition(
      position: slideTween.animate(curvedAnimation),
      child: FadeTransition(opacity: secondaryFadeTween.animate(secondaryAnimation), child: child),
    );
  }

  static Widget _fullScreenDialogTransitionsBuilder(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    final slideTween = Tween<Offset>(begin: const Offset(0, 1), end: const Offset(0, 0));

    final curvedAnimation = CurvedAnimation(parent: animation, curve: Curves.easeOutQuad);

    final secondaryFadeTween = Tween<double>(begin: 1, end: 0);

    return SlideTransition(
      position: slideTween.animate(curvedAnimation),
      child: FadeTransition(opacity: secondaryFadeTween.animate(secondaryAnimation), child: child),
    );
  }
}
