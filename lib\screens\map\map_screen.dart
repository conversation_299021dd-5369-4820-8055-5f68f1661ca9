import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_image_platform_interface/cached_network_image_platform_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:venvi/global_view_models/map_view_model.dart';
import 'package:venvi/models/map_model.dart';
import 'package:venvi/screens/bottom_nav/root_tab_tap_view_model.dart';
import 'package:venvi/screens/map/map_screen_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/shimmer_square.dart';

class MapScreen extends HookWidget {
  const MapScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final pageController = usePageController();

    return BlocProvider(
      create: (context) => MapScreenViewModel(pageController),
      child: Scaffold(
        appBar: const GlassAppBar(isRootScreen: true),
        body: BlocBuilder<MapViewModel, List<MapModel>?>(
          builder: (context, state) => state?.isNotEmpty == true
              ? Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(
                        AppTheme.screenPadding,
                        0,
                        AppTheme.screenPadding,
                        AppTheme.widgetPaddingSmall,
                      ),
                      child: ContentArea(
                        child: BlocBuilder<MapScreenViewModel, int>(
                          builder: (context, pageState) {
                            if (state == null || pageState >= state.length) {
                              return Center(child: Text('Map', style: Theme.of(context).textTheme.titleMedium));
                            }
                            final map = state[pageState];
                            return Center(
                              child: Text(map.name ?? 'Map', style: Theme.of(context).textTheme.titleMedium),
                            );
                          },
                        ),
                      ),
                    ),
                    Expanded(
                      child: BlocListener<RootTabTapViewModel, BottomTab?>(
                        listenWhen: (previous, current) => current == BottomTab.map,
                        listener: (context, state) {
                          if (pageController.hasClients) {
                            pageController.animateToPage(
                              0,
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                          context.read<RootTabTapViewModel>().clearTab();
                        },
                        child: PageView(
                          controller: pageController,
                          physics: const NeverScrollableScrollPhysics(),
                          children: state!
                              .map(
                                (map) => map.image?.downloadUrl != null
                                    ? CachedNetworkImage(
                                        imageRenderMethodForWeb: ImageRenderMethodForWeb.HttpGet,
                                        imageUrl: map.image?.downloadUrl ?? '',
                                        fit: BoxFit.contain,
                                        imageBuilder: (context, imageProvider) => InteractiveViewer(
                                          child: Image(image: imageProvider, fit: BoxFit.contain),
                                        ),
                                        placeholder: (context, url) => const ShimmerSquare(),
                                        errorWidget: (context, url, error) =>
                                            const ContentArea(fillWidth: false, child: Text('Error loading map')),
                                      )
                                    : const ContentArea(fillWidth: false, child: Text('No map found')),
                              )
                              .toList(),
                        ),
                      ),
                    ),
                    if (state.length > 1)
                      Padding(
                        padding: const EdgeInsets.fromLTRB(
                          AppTheme.screenPadding,
                          AppTheme.widgetPaddingSmall,
                          AppTheme.screenPadding,
                          0,
                        ),
                        child: ContentArea(
                          padding: const EdgeInsets.symmetric(
                            vertical: AppTheme.widgetPaddingSmall,
                            horizontal: AppTheme.widgetPadding,
                          ),
                          child: BlocBuilder<MapScreenViewModel, int>(
                            builder: (context, pageState) {
                              return Row(
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.chevron_left),
                                    onPressed: pageState > 0
                                        ? () {
                                            pageController.previousPage(
                                              duration: const Duration(milliseconds: 300),
                                              curve: Curves.easeInOut,
                                            );
                                          }
                                        : null,
                                  ),
                                  const Spacer(),
                                  Text(
                                    '${pageState + 1} of ${state.length}',
                                    style: Theme.of(context).textTheme.titleMedium,
                                  ),
                                  const Spacer(),
                                  IconButton(
                                    icon: const Icon(Icons.chevron_right),
                                    onPressed: pageState < state.length - 1
                                        ? () {
                                            pageController.nextPage(
                                              duration: const Duration(milliseconds: 300),
                                              curve: Curves.easeInOut,
                                            );
                                          }
                                        : null,
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                      ),
                  ],
                )
              : const ContentArea(fillWidth: false, child: Text('No maps found')),
        ),
      ),
    );
  }
}
