enum ImageType {
  logo(storageDirectory: 'logo'),
  banner(storageDirectory: 'banner'),
  homeScreenGallery(storageDirectory: 'homeScreenGallery'),
  reusableEventPrimary(storageDirectory: 'reusableEventPrimary'),
  eventPrimary(storageDirectory: 'eventPrimary'),
  eventGallery(storageDirectory: 'eventGallery'),
  map(storageDirectory: 'maps'),
  generalContentPrimary(storageDirectory: 'generalContentPrimary'),
  generalContentTiles(storageDirectory: 'generalContentTiles'),
  ad(storageDirectory: 'imageAds'),
  manualParticipantPhoto(storageDirectory: 'participantPhotos');

  final String storageDirectory;

  const ImageType({required this.storageDirectory});
}
