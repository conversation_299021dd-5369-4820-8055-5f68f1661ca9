import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/constants/rules.dart';
import 'package:venvi/constants/tier.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/custom_classes/date_fields.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/event_view_model.dart';
import 'package:venvi/global_view_models/named_doc_state.dart';
import 'package:venvi/global_view_models/named_doc_view_model.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/global_view_models/permission_view_model.dart';
import 'package:venvi/global_view_models/venue_view_model.dart';
import 'package:venvi/models/con_model.dart';
import 'package:venvi/models/event_model.dart';
import 'package:venvi/models/event_participant_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/venue_model.dart';
import 'package:venvi/models/venue_snippet_model.dart';
import 'package:venvi/repositories/event_repository.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/router.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_manual_participant_screen.dart';
import 'package:venvi/screen_dialogs/profile_search/profile_search_result.dart';
import 'package:venvi/screen_dialogs/profile_search/profile_search_screen.dart';
import 'package:venvi/screen_profile/profile_screen.dart';
import 'package:venvi/screens/edit_events/edit_event_state.dart';
import 'package:venvi/screens/edit_events/edit_event_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/bottom_sheets.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/image_selector.dart';
import 'package:venvi/utils/logger.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/utils/starter_tier_limit_checker.dart';
import 'package:venvi/utils/time_utils.dart';
import 'package:venvi/widgets/accent_label.dart';
import 'package:venvi/widgets/agenda_group.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/editor_scaffold.dart';
import 'package:venvi/widgets/glass_drop_down.dart';
import 'package:venvi/widgets/glass_tile_button.dart';
import 'package:venvi/widgets/glass_tile_icon_button.dart';
import 'package:venvi/widgets/image_updater_view.dart';
import 'package:venvi/widgets/input_error_message.dart';
import 'package:venvi/widgets/profile_item.dart';
import 'package:venvi/widgets/restricted_view.dart';
import 'package:venvi/widgets/surface_input_field.dart';

class EditEventScreen extends HookWidget {
  static const double _conflictBorderWidth = 2;

  final String? eventId;
  final bool isNewEvent;
  final bool multiAdd;

  const EditEventScreen({super.key, this.eventId, required this.isNewEvent, this.multiAdd = false});

  @override
  Widget build(BuildContext context) {
    // Check event limit for starter tier cons if creating a new event
    if (isNewEvent) {
      useEffect(() {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (StarterTierLimitChecker.isEventLimitReached(context)) {
            Dialogs.showPremiumFeatureDialog(context, PremiumUpgradeDialogType.eventLimit).then((value) {
              if (context.mounted && value != true) {
                context.pop();
              }
            });
          }
        });
        return null;
      }, []);
    }

    final titleController = useTextEditingController();
    final descController = useTextEditingController();

    final participantViewModel = context.read<ParticipantViewModel>();
    final locationModel = context.read<ConViewModel>().state?.location;

    return EditorScaffold<EditEventViewModel, EditEventState, EventViewModel, List<EventModel>?>(
      postApplyChangesOverride: multiAdd
          ? (context) {
              context.pop();
              WidgetsBinding.instance.addPostFrameCallback((_) {
                context.go('${context.read<ConData>().conPath}/admin/create-event');
              });
            }
          : null,
      checkForExternalConflicts: !isNewEvent,
      externalCubit: context.read<EventViewModel>(),
      buildState: (externalCubitState) {
        late final EventModel initialModel;
        if (eventId != null) {
          try {
            final loadedEvent = externalCubitState?.firstWhere((element) => element.id == eventId);
            if (loadedEvent == null) {
              return null;
            }
            if (isNewEvent) {
              initialModel = loadedEvent.copyWith(id: null, copyOf: eventId);
            } else {
              initialModel = loadedEvent;
            }
          } catch (e, stackTrace) {
            Logger.error(exception: e, stackTrace: stackTrace, message: 'Failed to load event');
            return null;
          }
        } else {
          initialModel = const EventModel();
        }

        final startTime = TimeUtils.convertToConTimeZone(locationModel, initialModel.startTime);
        final endTime = TimeUtils.convertToConTimeZone(locationModel, initialModel.endTime);

        return EditEventState(
          eventModel: initialModel,
          startDay: startTime != null
              ? DateFields(year: startTime.year, month: startTime.month, day: startTime.day)
              : null,
          startTime: startTime != null ? TimeOfDay(hour: startTime.hour, minute: startTime.minute) : null,
          endDay: endTime != null ? DateFields(year: endTime.year, month: endTime.month, day: endTime.day) : null,
          endTime: endTime != null ? TimeOfDay(hour: endTime.hour, minute: endTime.minute) : null,
          conflictEvents: context.read<EventViewModel>().checkEventConflicts(initialModel),
        );
      },
      initViewModel: (state) => EditEventViewModel(
        state,
        context.read<ConData>(),
        context.read<EventViewModel>(),
        context.read<EventRepository>(),
        context.read<ImageRepository>(),
        context.read<ConViewModel>(),
      ),
      title: eventId == null
          ? const Text('New Event')
          : isNewEvent
          ? const Text('New Copy of Event')
          : const Text('Edit Event'),
      successMessage: isNewEvent ? 'Event Created' : 'Event Updated',
      buildContent: (context, viewModel, state) {
        final eventModel = state.eventModel;

        if (eventModel.title != null && titleController.text != eventModel.title) {
          titleController.text = eventModel.title!;
        }
        if (eventModel.desc != null && descController.text != eventModel.desc) {
          descController.text = eventModel.desc!;
        }

        return ListView(
          padding: const EdgeInsets.all(AppTheme.screenPadding),
          children: [
            ContentGroup(
              title: 'Title',
              children: [
                ContentArea(
                  padding: EdgeInsets.zero,
                  child: SurfaceInputField(
                    child: TextField(
                      controller: titleController,
                      onChanged: (value) => viewModel.setTitle(value),
                      textInputAction: TextInputAction.next,
                      textCapitalization: TextCapitalization.words,
                      textAlign: TextAlign.center,
                      maxLength: InputConstants.maxEventTitleLength,
                      decoration: const InputDecoration(hintText: 'Title', counterText: ''),
                    ),
                  ),
                ),
                if (eventModel.title?.isNotEmpty != true) const InputErrorMessage(message: 'Title is required'),
              ],
            ),
            RestrictedView(
              rule: Rules.editEvents,
              child: Padding(
                padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                child: ContentArea(
                  padding: EdgeInsets.zero,
                  child: SwitchListTile(
                    title: const Text('Spotlight Event'),
                    secondary: Icon(eventModel.spotlight ?? false ? Icons.star : Icons.star_border),
                    value: eventModel.spotlight ?? false,
                    onChanged: (value) => viewModel.toggleSpotlight(),
                  ),
                ),
              ),
            ),
            BlocBuilder<ConViewModel, ConModel?>(
              builder: (context, state) => state?.tier == Tier.premium
                  ? BlocConsumer<EditEventViewModel, EditEventState>(
                      listenWhen: (previous, current) =>
                          previous.conflictEvents == null && current.conflictEvents != null,
                      listener: (context, state) => Dialogs.showInfoDialog(
                        context,
                        title: 'Event Conflict',
                        message:
                            'These events are happening at the same venue and have overlaping times\n• ${state.conflictEvents?.map((e) => e.title ?? '').join('\n• ')}',
                        isSelectable: true,
                      ),
                      builder: (context, state) => state.conflictEvents != null
                          ? Center(
                              child: Container(
                                margin: const EdgeInsets.only(top: AppTheme.widgetPadding),
                                width: AppTheme.maxContentWidth,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(AppTheme.borderRadius + _conflictBorderWidth),
                                  border: Border.all(
                                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                                    width: _conflictBorderWidth,
                                  ),
                                ),
                                child: AgendaGroup(
                                  title: 'Conflicting Events',
                                  indicateWhenCurrent: false,
                                  fadeWhenEnded: false,
                                  events: state.conflictEvents ?? [],
                                ),
                              ),
                            )
                          : SizedBox.shrink(),
                    )
                  : SizedBox.shrink(),
            ),
            RestrictedView(
              rule: Rules.editEvents,
              child: Padding(
                padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                child: ContentGroup(
                  title: 'Day and Time',
                  children: [
                    _buildDateTimeTile(
                      context: context,
                      label: 'Start Day',
                      errorMessage: 'Select Day',
                      value: state.startDay != null ? DateFormat.MMMEd().format(state.startDay!.toDateTime()) : null,
                      onTap: () => _showDatePicker(
                        context,
                        eventModel.startTime,
                        (selectedDate) => viewModel.setStartDay(selectedDate),
                      ),
                    ),
                    _buildDateTimeTile(
                      context: context,
                      label: 'Start Time',
                      errorMessage: 'Select Time',
                      value: state.startTime != null
                          ? DateFormat.jm().format(
                              DateTime.now().copyWith(hour: state.startTime!.hour, minute: state.startTime!.minute),
                            )
                          : null,
                      onTap: () => _showTimePicker(
                        context,
                        eventModel.startTime,
                        (selectedTime) => viewModel.setStartTime(selectedTime),
                      ),
                    ),
                    _buildDateTimeTile(
                      context: context,
                      label: 'End Day',
                      errorMessage: 'Select Day',
                      value: state.endDay != null ? DateFormat.MMMEd().format(state.endDay!.toDateTime()) : null,
                      onTap: () => _showDatePicker(
                        context,
                        eventModel.endTime,
                        (selectedDate) => viewModel.setEndDay(selectedDate),
                      ),
                    ),
                    _buildDateTimeTile(
                      context: context,
                      label: 'End Time',
                      errorMessage: 'Select Time',
                      value: state.endTime != null
                          ? DateFormat.jm().format(
                              DateTime.now().copyWith(hour: state.endTime!.hour, minute: state.endTime!.minute),
                            )
                          : null,
                      onTap: () => _showTimePicker(
                        context,
                        eventModel.endTime,
                        (selectedTime) => viewModel.setEndTime(selectedTime),
                      ),
                    ),
                    Center(child: Text('Duration: ${_getDurationString(eventModel.startTime, eventModel.endTime)}')),
                    if (eventModel.startTime != null &&
                        eventModel.endTime != null &&
                        TimeUtils.getConDay(locationModel, eventModel.startTime)?.day !=
                            TimeUtils.getConDay(locationModel, eventModel.endTime)?.day)
                      const Padding(
                        padding: EdgeInsets.only(top: AppTheme.widgetPaddingSmall),
                        child: AccentLabel(text: 'Overnight Event', isOnContainer: true),
                      ),
                  ],
                ),
              ),
            ),
            RestrictedView(
              rule: Rules.editEvents,
              child: Padding(
                padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                child: BlocBuilder<VenueViewModel, List<VenueModel>?>(
                  builder: (context, venueState) => ContentGroup(
                    title: 'Venue',
                    child: GlassDropDown<VenueSnippetModel>(
                      hint: 'Venue Name',
                      value: eventModel.venue,
                      onChanged: (newValue) => viewModel.setVenue(newValue),
                      items: (venueState ?? [])
                          .map((venueModel) => VenueSnippetModel.fromVenueModel(venueModel))
                          .toList(),
                      getItemLabel: (item) => item.name ?? '',
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: AppTheme.widgetPadding),
            ContentGroup(
              title: 'Event Image',
              children: [
                ImageUpdaterView(
                  imageUrl: eventModel.getImageData(eventModel.primaryImageId)?.downloadUrl,
                  imageFile: state.primaryImageOverrideFile,
                  onTap: () => BottomSheets.showEventImageBottomSheet(
                    context,
                    (model) => viewModel.setPrimaryImageReference(model),
                    (file) => viewModel.setPrimaryImageFile(file),
                  ),
                ),
                if (eventModel.primaryImageId != null || state.primaryImageOverrideFile != null)
                  Padding(
                    padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                    child: Center(
                      child: OutlinedButton.icon(
                        icon: const Icon(Icons.delete),
                        label: const Text('Remove Image'),
                        onPressed: () async {
                          final confirmDelete = await Dialogs.showConfirmationDialog(context, title: 'Remove Image?');
                          if (confirmDelete == true) {
                            viewModel.removePrimaryImage();
                          }
                        },
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: AppTheme.widgetPadding),
            ContentGroup(
              title: 'Gallery',
              padHorizontally: false,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(
                    AppTheme.widgetPadding,
                    0,
                    AppTheme.widgetPadding,
                    AppTheme.widgetPadding,
                  ),
                  child: Wrap(
                    spacing: AppTheme.widgetPaddingSmall,
                    runSpacing: AppTheme.widgetPaddingSmall,
                    alignment: WrapAlignment.center,
                    children: [
                      ...eventModel.galleryImageIds
                              ?.map<Widget>(
                                (imageId) => SizedBox(
                                  width: 150,
                                  height: 150,
                                  child: ImageUpdaterView(
                                    imageUrl: eventModel.getImageData(imageId)?.downloadUrl,
                                    icon: Icons.delete,
                                    onTap: () async {
                                      final confirmDelete = await Dialogs.showConfirmationDialog(
                                        context,
                                        title: 'Remove Image?',
                                      );
                                      if (confirmDelete == true) {
                                        viewModel.removeGalleryImageReference(imageId);
                                      }
                                    },
                                  ),
                                ),
                              )
                              .toList() ??
                          [],
                      ...state.galleryImageFiles
                              ?.map<Widget>(
                                (image) => SizedBox(
                                  width: 150,
                                  height: 150,
                                  child: ImageUpdaterView(
                                    imageFile: image,
                                    icon: Icons.delete,
                                    onTap: () async {
                                      final confirmDelete = await Dialogs.showConfirmationDialog(
                                        context,
                                        title: 'Remove Image?',
                                      );
                                      if (confirmDelete == true && context.mounted) {
                                        viewModel.removeGalleryImageFile(image);
                                      }
                                    },
                                  ),
                                ),
                              )
                              .toList() ??
                          [],
                    ],
                  ),
                ),
                if ((eventModel.galleryImageIds?.length ?? 0) + (state.galleryImageFiles?.length ?? 0) <
                    InputConstants.maxEventGalleryImages)
                  GlassTileIconButton(
                    icon: Icons.add,
                    tooltip: 'Add Image',
                    onTap: () async {
                      final file = await ImageSelector().selectImage(context, cropAspectRatio: 1);
                      if (file != null) {
                        viewModel.addGalleryImageFile(file);
                      }
                    },
                  ),
              ],
            ),
            const SizedBox(height: AppTheme.widgetPadding),
            ContentGroup(
              title: 'Description',
              child: ContentArea(
                padding: EdgeInsets.zero,
                child: SurfaceInputField(
                  child: TextField(
                    controller: descController,
                    onChanged: (value) => viewModel.setDesc(value),
                    textInputAction: TextInputAction.newline,
                    maxLines: 10,
                    textCapitalization: TextCapitalization.sentences,
                    maxLength: InputConstants.maxEventDescLength,
                    decoration: const InputDecoration(
                      contentPadding: EdgeInsets.all(AppTheme.widgetPadding),
                      hintText: 'Description',
                      counterText: '',
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: AppTheme.widgetPadding),
            ContentGroup(
              title: 'Type',
              child: BlocBuilder<NamedDocViewModel, NamedDocState>(
                builder: (context, state) {
                  final eventTypes = context.read<NamedDocViewModel>().getEnabledEventTypes();
                  return Wrap(
                    alignment: WrapAlignment.center,
                    spacing: AppTheme.widgetPaddingSmall,
                    children: List.generate(eventTypes.length, (index) {
                      final eventType = eventTypes[index];
                      final conEventType = state.eventProperties?.eventTypes?[eventType];
                      final eventTypeForegroundColor = eventType.foregroundColor;
                      return ChoiceChip(
                        label: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              conEventType?.icon?.icon ?? eventType.defaultIcon.icon,
                              color: eventTypeForegroundColor,
                              size: 16,
                            ),
                            const SizedBox(width: AppTheme.widgetPaddingSmall),
                            Text(
                              conEventType?.name?.isNotEmpty == true ? conEventType!.name! : eventType.defaultText,
                              style: Theme.of(context).textTheme.labelMedium?.copyWith(color: eventTypeForegroundColor),
                            ),
                          ],
                        ),
                        checkmarkColor: eventTypeForegroundColor,
                        color: WidgetStateProperty.all<Color>(
                          eventType.color.withValues(alpha: AppTheme.coloredContainerOpacity),
                        ),
                        selected: eventModel.type == eventType,
                        onSelected: (selected) => viewModel.setEventType(eventType),
                      );
                    }),
                  );
                },
              ),
            ),
            const SizedBox(height: AppTheme.widgetPadding),
            ContentArea(
              padding: EdgeInsets.zero,
              child: SwitchListTile(
                title: const Text('Adults Only'),
                value: eventModel.adultOnly ?? false,
                onChanged: (value) => viewModel.setAdultOnly(value),
              ),
            ),
            const SizedBox(height: AppTheme.widgetPadding),
            ContentGroup(
              title: 'Tags',
              child: BlocBuilder<NamedDocViewModel, NamedDocState>(
                builder: (context, namedDocState) {
                  final allTags = namedDocState.eventProperties?.eventTags;
                  if (allTags == null || allTags.isEmpty) {
                    return const SizedBox.shrink();
                  }
                  return Wrap(
                    alignment: WrapAlignment.center,
                    spacing: AppTheme.widgetPaddingSmall,
                    children: List.generate(allTags.length, (index) {
                      final tag = allTags[index] ?? '';
                      return ChoiceChip(
                        visualDensity: VisualDensity.compact,
                        label: Text(tag),
                        labelStyle: Theme.of(
                          context,
                        ).textTheme.labelSmall?.copyWith(color: Theme.of(context).colorScheme.onPrimary),
                        selected: eventModel.tags?.contains(tag) ?? false,
                        onSelected: (selected) =>
                            eventModel.tags?.contains(tag) ?? false ? viewModel.removeTag(tag) : viewModel.addTag(tag),
                      );
                    }),
                  );
                },
              ),
            ),
            const SizedBox(height: AppTheme.widgetPadding),
            ContentGroup(
              title: 'Event Admin',
              subtitle: 'Event Admin can edit this event\n(Not visible to attendees)',
              padHorizontally: false,
              child: eventModel.adminId != null
                  ? BlocBuilder<ParticipantViewModel, List<ParticipantModel>?>(
                      builder: (context, state) {
                        final participant = participantViewModel.getParticipant(eventModel.adminId!);
                        if (participant == null) {
                          return Center(
                            child: CircularProgressIndicator(color: Theme.of(context).colorScheme.onPrimaryContainer),
                          );
                        }
                        return ProfileItem(
                          profile: participant,
                          leadingButton: IconButton(
                            tooltip: 'Remove Admin',
                            onPressed: () =>
                                Dialogs.showConfirmationDialog(
                                  context,
                                  title:
                                      'Remove ${participantViewModel.getParticipant(eventModel.adminId!)?.displayName ?? 'this user'} as the event admin?',
                                ).then((value) {
                                  if (value == true) {
                                    viewModel.removeAdmin();
                                  }
                                }),
                            icon: const Icon(Icons.delete),
                          ),
                          onTapOverride: () => AppRouter.pushFullScreenDialog(
                            context,
                            ProfileScreen(isFullScreenDialog: true, profileId: eventModel.adminId),
                          ),
                        );
                      },
                    )
                  : GlassTileIconButton(
                      icon: Icons.add,
                      tooltip: 'Add Admin',
                      onTap: () async {
                        if (context.read<ConViewModel>().isPremium()) {
                          await _showProfileSearch(
                            context,
                            eventModel,
                            false,
                            (participant) => viewModel.setAdmin(participant),
                          );
                        } else {
                          Dialogs.showPremiumFeatureDialog(context, PremiumUpgradeDialogType.feature);
                        }
                      },
                    ),
            ),
            Padding(
              padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
              child: ContentGroup(
                title: 'Participants',
                padHorizontally: false,
                children: [
                  BlocBuilder<ParticipantViewModel, List<ParticipantModel>?>(
                    builder: (context, state) {
                      final List<EventParticipantModel> participants = eventModel.participants ?? [];
                      if (participants.isEmpty) {
                        return const SizedBox.shrink();
                      }

                      final participantViewModel = context.read<ParticipantViewModel>();

                      return ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: participants.length,
                        itemBuilder: (context, index) {
                          final eventParticipantModel = participants[index];
                          final participantId = eventParticipantModel.participantId;
                          final participantModel = participantId != null
                              ? participantViewModel.getParticipant(participantId)
                              : null;

                          if (participantId == null || participantModel == null) {
                            return Center(
                              child: CircularProgressIndicator(color: Theme.of(context).colorScheme.onPrimaryContainer),
                            );
                          }

                          return ProfileItem(
                            profile: participantModel,
                            roleId: eventParticipantModel.roleId,
                            showRole: true,
                            leadingButton: IconButton(
                              tooltip: 'Remove Participant',
                              onPressed: () =>
                                  Dialogs.showConfirmationDialog(
                                    context,
                                    title: 'Remove ${participantModel.displayName ?? 'this user'} as a participant?',
                                  ).then((value) {
                                    if (value == true) {
                                      viewModel.removeParticipant(participantId);
                                    }
                                  }),
                              icon: const Icon(Icons.delete),
                            ),
                            onTapOverride: () => AppRouter.pushFullScreenDialog(
                              context,
                              ProfileScreen(isFullScreenDialog: true, profileId: participantId),
                            ),
                          );
                        },
                      );
                    },
                  ),
                  if ((eventModel.participantIds?.length ?? 0) < InputConstants.maxParticipants)
                    GlassTileIconButton(
                      icon: Icons.add,
                      tooltip: 'Add Participant',
                      onTap: () async => await _showProfileSearch(context, eventModel, true, (participant) async {
                        final selectedRole = await Dialogs.showRoleSelectorDialog(
                          context,
                          participant.id!,
                          participant.allRoles ?? [],
                        );
                        if (selectedRole != null) {
                          viewModel.addParticipant(participant, selectedRole);
                        }
                      }),
                    ),
                ],
              ),
            ),
            if (!isNewEvent)
              RestrictedView(
                rule: Rules.editEvents,
                child: Padding(
                  padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                  child: ContentArea(
                    padding: EdgeInsets.zero,
                    child: GlassTileButton(
                      text: 'Delete Event',
                      icon: Icons.delete,
                      onTap: () async {
                        final confirmDelete = await Dialogs.showConfirmationDialog(
                          context,
                          title: 'Delete Event?',
                          message: eventModel.title,
                        );
                        if (confirmDelete == true && context.mounted) {
                          final result = await viewModel.deleteEvent();
                          if (context.mounted) {
                            if (result) {
                              SnackBars.showInfoSnackBar(context, 'Event deleted');
                              context.pop();
                              context.pop();
                            } else {
                              SnackBars.showInfoSnackBar(context, 'Failed to delete event');
                            }
                          }
                        }
                      },
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  Future<void> _showProfileSearch(
    BuildContext context,
    EventModel eventModel,
    bool allowManual,
    Function(ParticipantModel participant) onParticipantSelected,
  ) async {
    final excludedIds = eventModel.participants?.map((e) => e.participantId).whereType<String>().toList() ?? [];
    if (eventModel.adminId != null) {
      excludedIds.add(eventModel.adminId!);
    }

    final permissionsState = context.read<PermissionViewModel>().state;
    final permissionToEditParticipants =
        permissionsState != null &&
        permissionsState.any((userRole) => Rules.editParticipantsAndRoles.permissions.contains(userRole));

    final profileSearchResult = await AppRouter.pushFullScreenDialog<ProfileSearchResult?>(
      context,
      ProfileSearchScreen(
        excludedIds: excludedIds,
        showManualEntries: allowManual,
        bottomWidget: allowManual && permissionToEditParticipants
            ? ContentArea(
                padding: EdgeInsets.zero,
                child: GlassTileButton(
                  text: 'Add New Participant Manually',
                  icon: Icons.add,
                  onTap: () async {
                    final manualParticipant = await AppRouter.pushFullScreenDialog<ParticipantModel?>(
                      context,
                      const EditManualParticipantScreen(isNewParticipant: true),
                    );
                    if (manualParticipant != null && context.mounted) {
                      context.pop();
                      onParticipantSelected(manualParticipant);
                    }
                  },
                ),
              )
            : null,
      ),
    );
    if (profileSearchResult != null && context.mounted) {
      if (profileSearchResult.participantModel != null) {
        onParticipantSelected(profileSearchResult.participantModel!);
      } else if (profileSearchResult.profileModel?.id != null) {
        if (permissionToEditParticipants) {
          final participant = ParticipantModel.fromProfileModel(profileSearchResult.profileModel!, null, null);
          await context.read<ParticipantRepository>().addParticipant(
            context.read<ConData>(),
            participant.id!,
            participant,
          );
          onParticipantSelected(participant);
        } else {
          Dialogs.showInfoDialog(
            context,
            title: 'Permission Denied',
            message: 'Contact an admin to add this user as a participant',
          );
        }
      }
    }
  }

  Widget _buildDateTimeTile({
    required BuildContext context,
    required String label,
    required String errorMessage,
    required String? value,
    required Function() onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppTheme.borderRadius),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppTheme.widgetPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: AppTheme.subtleWidgetOpacity),
              ),
            ),
            if (value != null)
              Text(
                value,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onPrimaryContainer),
              ),
            if (value == null) InputErrorMessage(message: errorMessage),
          ],
        ),
      ),
    );
  }

  Future<void> _showDatePicker(BuildContext context, Timestamp? initialDate, Function(DateFields) onSelected) async {
    final conModel = context.read<ConViewModel>().state;
    final now = Timestamp.now().millisecondsSinceEpoch;
    final startDate =
        conModel?.startDate ?? Timestamp.fromMillisecondsSinceEpoch(now - const Duration(days: 365).inMilliseconds);
    final endDateMilliseconds = conModel?.endDate?.millisecondsSinceEpoch;
    final endDate = endDateMilliseconds != null
        ? Timestamp.fromMillisecondsSinceEpoch(endDateMilliseconds - Duration(hours: 12).inMilliseconds)
        : Timestamp.fromMillisecondsSinceEpoch(now + const Duration(days: 365 * 3).inMilliseconds);

    final datePicked = await Dialogs.showConTimeDatePickerDialog(
      context,
      conModel?.location,
      initialDate,
      startDate,
      endDate,
    );
    if (datePicked != null) {
      onSelected(datePicked);
    }
  }

  Future<void> _showTimePicker(BuildContext context, Timestamp? initialTime, Function(TimeOfDay) onSelected) async {
    final conModel = context.read<ConViewModel>().state;
    final timePicked = await Dialogs.showConTimeTimePickerDialog(context, conModel?.location, initialTime);
    if (timePicked != null) {
      onSelected(timePicked);
    }
  }

  String _getDurationString(Timestamp? startTime, Timestamp? endTime) {
    if (startTime == null || endTime == null) {
      return '';
    }
    final duration = Duration(milliseconds: endTime.millisecondsSinceEpoch - startTime.millisecondsSinceEpoch);
    final hours = duration.inHours;
    final minutes = (duration.inMinutes % 60);

    return '${hours}h ${minutes}m';
  }
}
