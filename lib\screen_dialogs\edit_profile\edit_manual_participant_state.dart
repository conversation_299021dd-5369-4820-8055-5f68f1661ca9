import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:venvi/models/participant_model.dart';

part 'edit_manual_participant_state.freezed.dart';

@freezed
sealed class EditManualParticipantState with _$EditManualParticipantState {
  const factory EditManualParticipantState({
    required ParticipantModel participantModel,
    XFile? profilePhotoOverrideFile,
  }) = _EditManualParticipantState;
}
