import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_manual_participant_state.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_manual_participant_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/image_selector.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/editor_scaffold.dart';
import 'package:venvi/widgets/image_updater_view.dart';
import 'package:venvi/widgets/surface_input_field.dart';

class EditManualParticipantScreen extends HookWidget {
  final String? participantId;
  final bool isNewParticipant;

  const EditManualParticipantScreen({super.key, required this.isNewParticipant, this.participantId});

  @override
  Widget build(BuildContext context) {
    final displayNameController = useTextEditingController();
    final conHeadlineController = useTextEditingController();
    final conBioController = useTextEditingController();

    return EditorScaffold<
      EditManualParticipantViewModel,
      EditManualParticipantState,
      ParticipantViewModel,
      List<ParticipantModel>?
    >(
      isFullScreenDialog: true,
      externalCubit: context.read<ParticipantViewModel>(),
      buildState: (externalCubitState) {
        ParticipantModel? participant;
        if (isNewParticipant) {
          participant = const ParticipantModel(manualEntry: true);
        } else if (participantId != null) {
          try {
            participant = externalCubitState?.firstWhere((element) => element.id == participantId);
          } catch (e) {
            return null;
          }
        }
        if (participant == null && !isNewParticipant) {
          return null;
        }

        return EditManualParticipantState(participantModel: participant!);
      },
      initViewModel: (state) {
        displayNameController.text = state.participantModel.displayName ?? '';
        conHeadlineController.text = state.participantModel.conHeadline ?? '';
        conBioController.text = state.participantModel.conBio ?? '';

        return EditManualParticipantViewModel(
          state.participantModel,
          context.read<ConData>(),
          context.read<ParticipantRepository>(),
          context.read<ImageRepository>(),
          participantId,
          isNewParticipant,
        );
      },
      title: const Text('Edit Profile'),
      successMessage: participantId != null ? 'Participant Updated' : 'Participant Created',
      buildContent: (context, viewModel, state) => ListView(
        padding: const EdgeInsets.all(AppTheme.screenPadding),
        children: [
          ContentGroup(
            title: 'Display Name',
            child: SurfaceInputField(
              child: TextField(
                controller: displayNameController,
                onChanged: (value) => viewModel.setDisplayName(value),
                textInputAction: TextInputAction.next,
                textCapitalization: TextCapitalization.words,
                textAlign: TextAlign.center,
                maxLength: InputConstants.maxProfileDisplayNameLength,
                decoration: const InputDecoration(hintText: 'Display Name', counterText: ''),
              ),
            ),
          ),
          const SizedBox(height: AppTheme.widgetPadding),
          ContentGroup(
            title: 'Profile Photo',
            child: ImageUpdaterView(
              imageUrl: state.participantModel.manualPhoto?.downloadUrl ?? state.participantModel.photoUrl,
              imageFile: state.profilePhotoOverrideFile,
              onTap: () async {
                final file = await ImageSelector().selectImage(context, cropAspectRatio: 1);
                if (file != null) {
                  viewModel.setProfilePhotoFile(file);
                }
              },
            ),
          ),
          const SizedBox(height: AppTheme.widgetPadding),
          ContentArea(
            padding: EdgeInsets.zero,
            child: SurfaceInputField(
              child: TextField(
                controller: conHeadlineController,
                onChanged: (value) => viewModel.setConHeadline(value),
                textInputAction: TextInputAction.newline,
                maxLines: 5,
                textCapitalization: TextCapitalization.sentences,
                maxLength: InputConstants.maxProfileHeadlineLength,
                decoration: const InputDecoration(
                  contentPadding: EdgeInsets.all(AppTheme.widgetPadding),
                  hintText: 'Headline',
                  counterText: '',
                ),
              ),
            ),
          ),
          const SizedBox(height: AppTheme.widgetPadding),
          ContentArea(
            padding: EdgeInsets.zero,
            child: SurfaceInputField(
              child: TextField(
                controller: conBioController,
                onChanged: (value) => viewModel.setConBio(value),
                textInputAction: TextInputAction.newline,
                maxLines: 10,
                textCapitalization: TextCapitalization.sentences,
                maxLength: InputConstants.maxProfileBioLength,
                decoration: const InputDecoration(
                  contentPadding: EdgeInsets.all(AppTheme.widgetPadding),
                  hintText: 'Bio',
                  counterText: '',
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
