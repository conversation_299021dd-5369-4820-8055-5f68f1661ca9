import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:uuid/uuid.dart';
import 'package:venvi/constants/image_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/utils/logger.dart';

class ImageRepository {
  final FirebaseStorage _storage;

  ImageRepository(this._storage);

  ImageModel createImageModel(ConData conData, ImageType imageType, {String? name}) {
    final fileName = name ?? const Uuid().v4();
    final storageDir = 'orgs/${conData.orgId}/cons/${conData.conId}/${imageType.storageDirectory}';
    return _createImageModelFromPath(storageDir, fileName);
  }

  ImageModel createProfilePhotoImageModel(String userId) {
    final storageDir = 'profilePhotos';
    return _createImageModelFromPath(storageDir, userId);
  }

  ImageModel _createImageModelFromPath(String storageDir, String fileName) {
    final storagePath = '$storageDir/$fileName';
    final downloadUrl =
        'https://firebasestorage.googleapis.com/v0/b/${_storage.bucket}/o/${Uri.encodeComponent(storagePath)}?alt=media';
    return ImageModel(id: fileName, storagePath: storagePath, downloadUrl: downloadUrl);
  }

  Future<bool> uploadImage(ImageModel imageModel, XFile file) async {
    final ref = _storage.ref(imageModel.storagePath);
    try {
      final bytes = await file.readAsBytes();
      final snapshot = await ref.putData(bytes, SettableMetadata(contentType: "image/webp"));
      return snapshot.state == TaskState.success;
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace, message: 'Failed uploading image: ${ref.fullPath}');
      return false;
    }
  }
}
