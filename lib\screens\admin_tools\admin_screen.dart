import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/constants/rules.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/permission_view_model.dart';
import 'package:venvi/screens/bottom_nav/root_tab_tap_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/utils/url_handler.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/glass_tile_button.dart';
import 'package:venvi/widgets/restricted_view.dart';

class AdminScreen extends HookWidget {
  const AdminScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final scrollController = useScrollController();

    final conPath = context.watch<ConData>().conPath;
    final roleViewModel = context.watch<PermissionViewModel>();

    final widgets = [
      if (roleViewModel.isPermitted(Rules.upgradeToPremium) && !context.watch<ConViewModel>().isPremium())
        ContentArea(
          padding: EdgeInsets.zero,
          child: GlassTileButton(
            icon: Icons.stars,
            text: 'Upgrade to Premium',
            isCentered: false,
            isAccent: true,
            onTap: () => Dialogs.showPremiumFeatureDialog(context, PremiumUpgradeDialogType.upgrade),
          ),
        ),
      if (roleViewModel.isPermitted(Rules.viewAnalytics))
        ContentGroup(
          title: 'Analytics',
          padHorizontally: false,
          children: [
            RestrictedView(
              rule: Rules.viewAnalytics,
              child: GlassTileButton(
                icon: Icons.analytics,
                text: 'Analytics',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/analytics'),
              ),
            ),
          ],
        ),
      if (roleViewModel.isPermitted(Rules.makeAnnouncements))
        ContentGroup(
          title: 'Announcements',
          padHorizontally: false,
          children: [
            RestrictedView(
              rule: Rules.makeAnnouncements,
              child: GlassTileButton(
                icon: Icons.campaign,
                text: 'Manage Announcements',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/announcement-manager'),
              ),
            ),
          ],
        ),
      if (roleViewModel.isPermitted(Rules.editOrg) ||
          roleViewModel.isPermitted(Rules.viewUnpublishedCons) ||
          roleViewModel.isPermitted(Rules.editRoles))
        ContentGroup(
          title: 'Organization',
          padHorizontally: false,
          children: [
            RestrictedView(
              rule: Rules.editOrg,
              child: GlassTileButton(
                icon: Icons.business_center,
                text: 'Manage Organization',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/org-manager'),
              ),
            ),
            if (!roleViewModel.isPermitted(Rules.editOrg))
              RestrictedView(
                rule: Rules.viewUnpublishedCons,
                child: GlassTileButton(
                  icon: Icons.list,
                  text: 'Organization Con List',
                  isCentered: false,
                  onTap: () => context.go('$conPath/admin/con-list'),
                ),
              ),
            RestrictedView(
              rule: Rules.editRoles,
              child: GlassTileButton(
                icon: Icons.admin_panel_settings,
                text: 'Permissions',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/permissions-manager'),
              ),
            ),
          ],
        ),
      if (roleViewModel.isPermitted(Rules.editCon) ||
          roleViewModel.isPermitted(Rules.editVenues) ||
          roleViewModel.isPermitted(Rules.editMaps))
        ContentGroup(
          title: 'Con',
          padHorizontally: false,
          children: [
            RestrictedView(
              rule: Rules.editCon,
              child: GlassTileButton(
                icon: Icons.edit,
                text: 'Manage Con',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/con-manager'),
              ),
            ),
            RestrictedView(
              rule: Rules.editVenues,
              child: GlassTileButton(
                icon: Icons.meeting_room,
                text: 'Venues',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/venue-manager'),
              ),
            ),
            RestrictedView(
              rule: Rules.editMaps,
              child: GlassTileButton(
                icon: Icons.map,
                text: 'Maps',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/map-manager'),
              ),
            ),
          ],
        ),
      if (roleViewModel.isPermitted(Rules.editHomeScreenGallery) ||
          roleViewModel.isPermitted(Rules.useReusableEventImages))
        ContentGroup(
          title: 'Photo Gallery',
          padHorizontally: false,
          children: [
            RestrictedView(
              rule: Rules.editHomeScreenGallery,
              child: GlassTileButton(
                icon: Icons.view_carousel,
                text: 'Home Screen Photo Gallery',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/home-screen-gallery'),
              ),
            ),
            RestrictedView(
              rule: Rules.useReusableEventImages,
              child: GlassTileButton(
                icon: Icons.photo_library,
                text: 'Reusable Event Image Library',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/reusable-event-image-library'),
              ),
            ),
          ],
        ),
      if (roleViewModel.isPermitted(Rules.editEventProperties) ||
          roleViewModel.isPermitted(Rules.editParticipantsAndRoles) ||
          roleViewModel.isPermitted(Rules.editEvents))
        ContentGroup(
          title: 'Events',
          padHorizontally: false,
          children: [
            RestrictedView(
              rule: Rules.editEventProperties,
              child: GlassTileButton(
                icon: Icons.label,
                text: 'Event Properties',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/event-properties-manager'),
              ),
            ),
            RestrictedView(
              rule: Rules.editParticipantsAndRoles,
              child: GlassTileButton(
                icon: Icons.co_present,
                text: 'Roles',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/roles'),
              ),
            ),
            RestrictedView(
              rule: Rules.editEvents,
              child: GlassTileButton(
                icon: Icons.event,
                text: 'Create Events',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/create-event'),
              ),
            ),
            RestrictedView(
              rule: Rules.editEvents,
              child: GlassTileButton(
                icon: Icons.warning,
                text: 'Conflict Detector',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/conflict-detector'),
              ),
            ),
          ],
        ),
      if (roleViewModel.isPermitted(Rules.editGeneralContent))
        ContentGroup(
          title: 'General Content',
          padHorizontally: false,
          children: [
            RestrictedView(
              rule: Rules.editGeneralContent,
              child: GlassTileButton(
                icon: Icons.web,
                text: 'Create General Content Page',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/create-general-content'),
              ),
            ),
            RestrictedView(
              rule: Rules.editGeneralContent,
              child: GlassTileButton(
                icon: Icons.low_priority,
                text: 'Reorder General Content Pages',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/reorder-general-content'),
              ),
            ),
          ],
        ),
      if (roleViewModel.isPermitted(Rules.importExportSchedule))
        ContentGroup(
          title: 'Tools',
          padHorizontally: false,
          children: [
            RestrictedView(
              rule: Rules.importExportSchedule,
              child: GlassTileButton(
                icon: Icons.swap_horiz,
                text: 'Import/Export Schedule',
                isCentered: false,
                onTap: () => context.go('$conPath/admin/import-export'),
              ),
            ),
          ],
        ),
      ContentArea(
        padding: EdgeInsets.zero,
        child: GlassTileButton(
          icon: Icons.contact_support,
          text: 'Contact Venvi Support',
          isCentered: false,
          onTap: () => UrlHandler.open(
            'mailto:<EMAIL>?subject=Venvi%20Support%20Request',
            onFailed: () => SnackBars.showInfoSnackBar(context, 'Please email <NAME_EMAIL>'),
          ),
        ),
      ),
    ];

    return Scaffold(
      appBar: const GlassAppBar(isRootScreen: true),
      body: BlocListener<RootTabTapViewModel, BottomTab?>(
        listenWhen: (previous, current) => current == BottomTab.admin,
        listener: (context, state) {
          if (scrollController.hasClients) {
            scrollController.animateTo(0, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
          }
          context.read<RootTabTapViewModel>().clearTab();
        },
        child: ListView.separated(
          controller: scrollController,
          padding: const EdgeInsets.all(AppTheme.screenPadding),
          itemCount: widgets.length,
          separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
          itemBuilder: (context, index) => widgets[index],
        ),
      ),
    );
  }
}
