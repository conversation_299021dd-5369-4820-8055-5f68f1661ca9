import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/permissions.dart';
import 'package:venvi/constants/rules.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/ad_view_model.dart';
import 'package:venvi/global_view_models/analytics_consent_view_model.dart';
import 'package:venvi/global_view_models/announcement_view_model.dart';
import 'package:venvi/global_view_models/auth_view_model.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/event_view_model.dart';
import 'package:venvi/global_view_models/general_content_view_model.dart';
import 'package:venvi/global_view_models/home_screen_gallery_view_model.dart';
import 'package:venvi/global_view_models/map_view_model.dart';
import 'package:venvi/global_view_models/metadata_view_model.dart';
import 'package:venvi/global_view_models/named_doc_view_model.dart';
import 'package:venvi/global_view_models/org_view_model.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/global_view_models/permission_view_model.dart';
import 'package:venvi/global_view_models/role_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/global_view_models/venue_view_model.dart';
import 'package:venvi/json_converters/color_converter.dart';
import 'package:venvi/models/con_model.dart';
import 'package:venvi/repositories/ad_repository.dart';
import 'package:venvi/repositories/announcement_repository.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/repositories/cloud_messaging_repository.dart';
import 'package:venvi/repositories/con_repository.dart';
import 'package:venvi/repositories/event_repository.dart';
import 'package:venvi/repositories/general_content_repository.dart';
import 'package:venvi/repositories/image_library_repository.dart';
import 'package:venvi/repositories/map_repository.dart';
import 'package:venvi/repositories/metadata_repository.dart';
import 'package:venvi/repositories/named_doc_repository.dart';
import 'package:venvi/repositories/org_repository.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/repositories/permission_repository.dart';
import 'package:venvi/repositories/role_repository.dart';
import 'package:venvi/repositories/schedule_download_tracker_repository.dart';
import 'package:venvi/repositories/user_repository.dart';
import 'package:venvi/repositories/venue_repository.dart';
import 'package:venvi/screens/bottom_nav/bottom_nav_loading_view_model.dart';
import 'package:venvi/screens/bottom_nav/root_tab_tap_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/widgets/adaptive_view.dart';
import 'package:venvi/widgets/analytics_consent_banner.dart';
import 'package:venvi/widgets/desktop_nav_bar.dart';
import 'package:venvi/widgets/gradient_background.dart';
import 'package:visibility_detector/visibility_detector.dart';

class BottomNavScreen extends StatefulWidget {
  final String? orgId;
  final String? conId;
  final StatefulNavigationShell navigationShell;

  const BottomNavScreen({super.key, required this.orgId, required this.conId, required this.navigationShell});

  @override
  State<BottomNavScreen> createState() => _BottomNavScreenState();
}

class _BottomNavScreenState extends State<BottomNavScreen> {
  static const Duration _initialFirestoreTimeout = Duration(seconds: 10);

  late final ConData? _conData;
  bool _initialAccessChecked = false;
  bool _redirectTriggered = false;

  void _redirectToHome({
    String message = 'Could not load con either because it does not exist or you do not have permission to view it',
  }) {
    if (_redirectTriggered) {
      return;
    }
    _redirectTriggered = true;
    setState(() {});

    Dialogs.showErrorDialog(context, message: message).then((value) {
      final context = this.context;
      if (context.mounted) {
        context.go('/home');
      }
    });
  }

  @override
  void initState() {
    _conData = ConData.tryCreate(orgId: widget.orgId, conId: widget.conId);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_conData == null) {
        _redirectToHome();
      } else {
        _checkInitialAccess().then((value) {
          _initialAccessChecked = true;
          setState(() {});
        });
      }
    });

    super.initState();
  }

  Future<void> _checkInitialAccess() async {
    final conData = _conData;
    if (conData == null) {
      return;
    }

    final conRepository = context.read<ConRepository>();
    final permissionRepository = context.read<PermissionRepository>();
    final authRepository = context.read<AuthRepository>();

    final conModel =
        await conRepository.getCon(conData, dataLocation: DataLocation.server, timeout: _initialFirestoreTimeout) ??
        await conRepository.getCon(conData, dataLocation: DataLocation.cache);
    if (conModel == null) {
      _redirectToHome();
      return;
    }

    if (conModel.isPublished == true) {
      return;
    }

    final userId = authRepository.currentUserId;
    if (userId == null) {
      _redirectToHome();
      return;
    }

    final permissionModel =
        await permissionRepository.getUserPermissions(
          conData.orgId,
          userId,
          dataLocation: DataLocation.server,
          timeout: _initialFirestoreTimeout,
        ) ??
        await permissionRepository.getUserPermissions(conData.orgId, userId, dataLocation: DataLocation.cache);
    if (permissionModel == null) {
      _redirectToHome();
      return;
    }

    final hasPermission = PermissionViewModel.isStatePermitted(permissionModel.permissions, Rules.viewUnpublishedCons);
    if (hasPermission != true) {
      _redirectToHome();
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    final conData = _conData;
    if (conData == null || !_initialAccessChecked || _redirectTriggered) {
      return _buildDefaultScreen(context);
    }

    return Provider.value(
      value: conData,
      child: BlocProvider(
        create: (context) => MetadataViewModel(
          conData,
          context.read<MetadataRepository>(),
          context.read<ScheduleDownloadTrackerRepository>(),
        ),
        child: Builder(
          builder: (context) => FutureBuilder(
            future: context.read<MetadataViewModel>().waitForLoading().then((value) => true),
            builder: (context, snapshot) => snapshot.hasData
                ? MultiBlocProvider(
                    providers: [
                      BlocProvider(
                        create: (context) =>
                            ConViewModel(conData, context.read<ConRepository>(), context.read<MetadataViewModel>()),
                      ),
                      BlocProvider(
                        create: (context) => PermissionViewModel(
                          conData,
                          context.read<PermissionRepository>(),
                          context.read<AuthViewModel>(),
                          context.read<MetadataViewModel>(),
                        ),
                      ),
                      BlocProvider(
                        create: (context) =>
                            OrgViewModel(conData, context.read<OrgRepository>(), context.read<MetadataViewModel>()),
                      ),
                      BlocProvider(
                        create: (context) => NamedDocViewModel(
                          conData,
                          context.read<NamedDocRepository>(),
                          context.read<MetadataViewModel>(),
                          context.read<CloudMessagingRepository>(),
                        ),
                      ),
                      BlocProvider(
                        create: (context) => ParticipantViewModel(
                          conData,
                          context.read<ParticipantRepository>(),
                          context.read<MetadataViewModel>(),
                        ),
                      ),
                      BlocProvider(
                        create: (context) =>
                            RoleViewModel(conData, context.read<RoleRepository>(), context.read<MetadataViewModel>()),
                      ),
                      BlocProvider(
                        create: (context) =>
                            EventViewModel(conData, context.read<EventRepository>(), context.read<MetadataViewModel>()),
                      ),
                      BlocProvider(
                        create: (context) => AnnouncementViewModel(
                          conData,
                          context.read<AnnouncementRepository>(),
                          context.read<MetadataViewModel>(),
                        ),
                      ),
                      BlocProvider(
                        create: (context) =>
                            VenueViewModel(conData, context.read<VenueRepository>(), context.read<MetadataViewModel>()),
                      ),
                      BlocProvider(
                        create: (context) =>
                            AdViewModel(conData, context.read<AdRepository>(), context.read<MetadataViewModel>()),
                      ),
                      BlocProvider(
                        create: (context) =>
                            MapViewModel(conData, context.read<MapRepository>(), context.read<MetadataViewModel>()),
                      ),
                      BlocProvider(
                        create: (context) => GeneralContentViewModel(
                          conData,
                          context.read<GeneralContentRepository>(),
                          context.read<MetadataViewModel>(),
                        ),
                      ),
                      BlocProvider(
                        create: (context) => HomeScreenGalleryViewModel(
                          conData,
                          context.read<ImageLibraryRepository>(),
                          context.read<MetadataViewModel>(),
                        ),
                      ),
                      BlocProvider(
                        create: (context) => BottomNavLoadingViewModel([
                          context.read<UserRepository>().setLastCon(
                            context.read<AuthRepository>().currentUserId,
                            conData,
                          ),
                          context.read<OrgViewModel>().waitForLoading(),
                          context.read<ConViewModel>().waitForLoading(),
                          context.read<PermissionViewModel>().waitForLoading(),
                          context.read<NamedDocViewModel>().waitForLoading(),
                          context.read<ParticipantViewModel>().waitForLoading(),
                          context.read<RoleViewModel>().waitForLoading(),
                          context.read<EventViewModel>().waitForLoading(),
                          context.read<AnnouncementViewModel>().waitForLoading(),
                          context.read<VenueViewModel>().waitForLoading(),
                          context.read<AdViewModel>().waitForLoading(),
                          context.read<MapViewModel>().waitForLoading(),
                          context.read<GeneralContentViewModel>().waitForLoading(),
                          context.read<HomeScreenGalleryViewModel>().waitForLoading(),
                        ]),
                      ),
                    ],
                    child: Builder(builder: (context) => _buildConShell(context)),
                  )
                : const Center(child: CircularProgressIndicator()),
          ),
        ),
      ),
    );
  }

  Widget _buildConShell(BuildContext context) {
    final colorHex = context.watch<ConViewModel>().state?.color;

    return Theme(
      data: AppTheme.createThemeData(colorHex, Theme.of(context).brightness),
      child: GradientBackground(
        color: colorHex != null ? ColorConverter.stringToColor(colorHex) : null,
        child: Stack(
          children: [
            BlocBuilder<BottomNavLoadingViewModel, LoadingState>(
              builder: (context, loadingState) {
                switch (loadingState) {
                  case LoadingState.notStarted:
                    return VisibilityDetector(
                      key: ValueKey('${context.read<ConData>().conWidgetKey}-BottomNavScreen'),
                      onVisibilityChanged: (info) {
                        if (info.visibleFraction >= 1) {
                          context.read<BottomNavLoadingViewModel>().load();
                          context.read<AnalyticsConsentViewModel>().tryShowInitialConsentDialog(context).then((value) {
                            if (context.mounted) {
                              context.read<UserViewModel>().enableUserInteractiveDeviceNotifications(
                                context.read<AuthRepository>(),
                              );
                            }
                          });
                        }
                      },
                      child: const Center(child: CircularProgressIndicator()),
                    );
                  case LoadingState.loading:
                    return const Center(child: CircularProgressIndicator());
                  case LoadingState.complete:
                    return BlocConsumer<PermissionViewModel, List<Permissions>?>(
                      listener: (context, permissionState) {
                        if (!_canAccessCon(context.read<ConViewModel>().state, permissionState)) {
                          _redirectToHome();
                          return;
                        }
                      },
                      buildWhen: (previous, current) =>
                          PermissionViewModel.isStatePermitted(previous, Rules.viewUnpublishedCons) !=
                          PermissionViewModel.isStatePermitted(current, Rules.viewUnpublishedCons),
                      builder: (context, permissionState) => BlocConsumer<ConViewModel, ConModel?>(
                        listener: (context, conState) {
                          if (!_canAccessCon(conState, permissionState)) {
                            _redirectToHome();
                            return;
                          }
                        },
                        buildWhen: (previous, current) => previous?.isPublished != current?.isPublished,
                        builder: (context, conState) {
                          if (!_canAccessCon(conState, permissionState)) {
                            return _buildDefaultScreen(context);
                          }

                          return _buildMainContent(context);
                        },
                      ),
                    );
                  case LoadingState.error:
                    _redirectToHome(message: 'Failed to load convention');
                    return _buildDefaultScreen(context);
                }
              },
            ),
            AnalyticsConsentBanner(),
          ],
        ),
      ),
    );
  }

  bool _canAccessCon(ConModel? conState, List<Permissions>? permissionState) {
    if (conState == null) {
      return false;
    }

    if (conState.isPublished == true) {
      return true;
    }

    if (permissionState == null) {
      return false;
    }

    return PermissionViewModel.isStatePermitted(permissionState, Rules.viewUnpublishedCons);
  }

  Widget _buildMainContent(BuildContext context) {
    // Reroutes if on admin tab but lost access such as signing out
    final showAdminTab = context.watch<PermissionViewModel>().isPermitted(Rules.seeAdminScreen);
    bool rerouting = false;
    if (!showAdminTab && widget.navigationShell.currentIndex == 4) {
      rerouting = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.navigationShell.goBranch(0);
        setState(() {});
      });
    }

    return rerouting
        ? const Scaffold()
        : BlocProvider(
            create: (context) => RootTabTapViewModel(),
            child: Builder(
              builder: (context) => AdaptiveView(
                desktopView: Scaffold(
                  body: Row(
                    children: [
                      const SizedBox(width: AppTheme.screenPadding),
                      DesktopNavBar(navigationShell: widget.navigationShell),
                      const SizedBox(width: AppTheme.screenPadding),
                      Expanded(child: widget.navigationShell),
                    ],
                  ),
                ),
                mobileView: Scaffold(
                  body: widget.navigationShell,
                  bottomNavigationBar: BottomNavigationBar(
                    items: [
                      const BottomNavigationBarItem(
                        activeIcon: Icon(Icons.home),
                        icon: Icon(Icons.home_outlined),
                        label: 'Home',
                      ),
                      const BottomNavigationBarItem(
                        activeIcon: Icon(Icons.favorite),
                        icon: Icon(Icons.favorite_outline),
                        label: 'Favorites',
                      ),
                      const BottomNavigationBarItem(
                        activeIcon: Icon(Icons.calendar_today),
                        icon: Icon(Icons.calendar_today_outlined),
                        label: 'Schedule',
                      ),
                      const BottomNavigationBarItem(
                        activeIcon: Icon(Icons.map),
                        icon: Icon(Icons.map_outlined),
                        label: 'Map',
                      ),
                      if (showAdminTab)
                        const BottomNavigationBarItem(
                          activeIcon: Icon(Icons.edit),
                          icon: Icon(Icons.edit_outlined),
                          label: 'Admin',
                        ),
                    ],
                    currentIndex: widget.navigationShell.currentIndex,
                    onTap: (index) => _onTap(context, index, context.read<ConData>()),
                  ),
                ),
              ),
            ),
          );
  }

  void _onTap(BuildContext context, int index, ConData conData) {
    FocusManager.instance.primaryFocus?.unfocus();

    if (index == widget.navigationShell.currentIndex) {
      if (GoRouter.of(context).state.uri.pathSegments.length == 3) {
        context.read<RootTabTapViewModel>().tapTab(BottomTab.values[index]);
      } else {
        widget.navigationShell.goBranch(index, initialLocation: true);
      }
    } else {
      widget.navigationShell.goBranch(index);
    }
  }

  Widget _buildDefaultScreen(BuildContext context) {
    return Container(width: double.infinity, height: double.infinity, color: Colors.black);
  }
}
