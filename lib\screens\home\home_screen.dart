import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_image_platform_interface/cached_network_image_platform_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/constants/rules.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/event_view_model.dart';
import 'package:venvi/global_view_models/general_content_view_model.dart';
import 'package:venvi/global_view_models/home_screen_gallery_view_model.dart';
import 'package:venvi/global_view_models/org_view_model.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/global_view_models/permission_view_model.dart';
import 'package:venvi/global_view_models/role_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/con_model.dart';
import 'package:venvi/models/event_model.dart';
import 'package:venvi/models/general_content_model.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/models/org_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/role_model.dart';
import 'package:venvi/screens/bottom_nav/root_tab_tap_view_model.dart';
import 'package:venvi/screens/home/<USER>';
import 'package:venvi/screens/home/<USER>';
import 'package:venvi/screens/home/<USER>';
import 'package:venvi/screens/home/<USER>';
import 'package:venvi/screens/home/<USER>';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/url_handler.dart';
import 'package:venvi/widgets/agenda_group.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/glass_tile_button.dart';
import 'package:venvi/widgets/host_con_button.dart';
import 'package:venvi/widgets/image_scroller.dart';
import 'package:venvi/widgets/minute_trigger.dart';
import 'package:venvi/widgets/my_account_icon_button.dart';
import 'package:venvi/widgets/profile_photo_view.dart';
import 'package:venvi/widgets/share_button.dart';
import 'package:venvi/widgets/shimmer_square.dart';

class HomeScreen extends HookWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final scrollController = useScrollController();

    final theme = Theme.of(context); // Used because role titles would not change when device brightness changes
    return Scaffold(
      appBar: GlassAppBar(
        isRootScreen: true,
        leading: IconButton(
          tooltip: 'Con Search',
          icon: const Icon(Icons.manage_search),
          onPressed: () => context.go('/home'),
        ),
        actions: const [MyAccountIconButton()],
      ),
      body: BlocListener<RootTabTapViewModel, BottomTab?>(
        listenWhen: (previous, current) => current == BottomTab.home,
        listener: (context, state) {
          if (scrollController.hasClients) {
            scrollController.animateTo(0, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
          }
          context.read<RootTabTapViewModel>().clearTab();
        },
        child: ListView(
          controller: scrollController,
          children: [
            BlocBuilder<OrgViewModel, OrgModel?>(
              builder: (context, state) =>
                  state != null && state.activeConId != null && state.activeConId != context.read<ConData>().conId
                  ? Padding(
                      padding: const EdgeInsets.fromLTRB(
                        AppTheme.screenPadding,
                        0,
                        AppTheme.screenPadding,
                        AppTheme.widgetPadding,
                      ),
                      child: ContentArea(
                        padding: EdgeInsets.zero,
                        child: GlassTileButton(
                          isAccent: true,
                          text: 'Switch to active con',
                          icon: Icons.swap_horiz,
                          subtitle: 'This is not the active con',
                          onTap: () => context.go('/${state.id}/${state.activeConId}'),
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
            BlocBuilder<ConViewModel, ConModel?>(
              builder: (context, state) => state?.banner?.downloadUrl != null
                  ? Center(
                      child: Container(
                        padding: const EdgeInsets.only(bottom: AppTheme.widgetPadding),
                        constraints: const BoxConstraints(maxWidth: AppTheme.maxContentWidth),
                        child: CachedNetworkImage(
                          imageRenderMethodForWeb: ImageRenderMethodForWeb.HttpGet,
                          imageUrl: state!.banner!.downloadUrl!,
                          width: double.infinity,
                          imageBuilder: (context, imageProvider) => AspectRatio(
                            aspectRatio: 2 / 1,
                            child: Image(image: imageProvider, fit: BoxFit.cover),
                          ),
                          placeholder: (context, url) => const AspectRatio(aspectRatio: 2 / 1, child: ShimmerSquare()),
                          errorWidget: (context, url, error) => const SizedBox.shrink(),
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
            BlocBuilder<HomeScreenGalleryViewModel, List<ImageModel>?>(
              builder: (context, state) {
                if (state == null || state.isEmpty) {
                  return const SizedBox.shrink();
                }

                return Padding(
                  padding: const EdgeInsets.only(bottom: AppTheme.widgetPadding),
                  child: ImageScroller(images: state),
                );
              },
            ),
            const AnnouncementView(isHighPriority: true),
            // const Padding(
            //   padding: EdgeInsets.only(bottom: AppTheme.widgetPadding),
            //   child: AdScroller(),
            // ),
            BlocProvider(
              create: (context) => HighlightedEventsViewModel(
                context.read<ConData>(),
                context.read<EventViewModel>(),
                context.read<UserViewModel>(),
                HighlightedEventsType.current,
              ),
              child: Builder(
                builder: (context) => MinuteTrigger(
                  onTrigger: () => context.read<HighlightedEventsViewModel>().refresh(),
                  child: BlocBuilder<HighlightedEventsViewModel, List<EventModel>?>(
                    builder: (context, state) => state != null && state.isNotEmpty
                        ? Padding(
                            padding: const EdgeInsets.fromLTRB(
                              AppTheme.screenPadding,
                              0,
                              AppTheme.screenPadding,
                              AppTheme.widgetPadding,
                            ),
                            child: AgendaGroup(title: 'Happening Now', events: state, indicateWhenCurrent: false),
                          )
                        : const SizedBox.shrink(),
                  ),
                ),
              ),
            ),
            BlocProvider(
              create: (context) => HighlightedEventsViewModel(
                context.read<ConData>(),
                context.read<EventViewModel>(),
                context.read<UserViewModel>(),
                HighlightedEventsType.upcoming,
              ),
              child: Builder(
                builder: (context) => MinuteTrigger(
                  onTrigger: () => context.read<HighlightedEventsViewModel>().refresh(),
                  child: BlocBuilder<HighlightedEventsViewModel, List<EventModel>?>(
                    builder: (context, state) => state != null && state.isNotEmpty
                        ? Padding(
                            padding: const EdgeInsets.fromLTRB(
                              AppTheme.screenPadding,
                              0,
                              AppTheme.screenPadding,
                              AppTheme.widgetPadding,
                            ),
                            child: AgendaGroup(title: 'Upcoming', events: state, indicateWhenCurrent: false),
                          )
                        : const SizedBox.shrink(),
                  ),
                ),
              ),
            ),
            BlocProvider(
              create: (context) => PlanningViewModel(context.read<ConData>(), context.read<ConViewModel>()),
              child: Builder(
                builder: (context) => MinuteTrigger(
                  onTrigger: () => context.read<PlanningViewModel>().refresh(),
                  child: BlocBuilder<PlanningViewModel, bool>(
                    builder: (context, state) => state
                        ? Padding(
                            padding: const EdgeInsets.fromLTRB(
                              AppTheme.screenPadding,
                              0,
                              AppTheme.screenPadding,
                              AppTheme.widgetPadding,
                            ),
                            child: ContentArea(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text('Plan your con'),
                                  const SizedBox(height: AppTheme.widgetPaddingSmall),
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      FocusManager.instance.primaryFocus?.unfocus();
                                      context.go('${context.read<ConData>().conPath}/schedule');
                                    },
                                    icon: const Icon(Icons.calendar_today),
                                    label: const Text('See Schedule'),
                                  ),
                                ],
                              ),
                            ),
                          )
                        : const SizedBox.shrink(),
                  ),
                ),
              ),
            ),
            BlocBuilder<GeneralContentViewModel, List<GeneralContentModel>?>(
              builder: (context, state) => state != null && state.isNotEmpty
                  ? Padding(
                      padding: const EdgeInsets.fromLTRB(
                        AppTheme.screenPadding,
                        0,
                        AppTheme.screenPadding,
                        AppTheme.widgetPadding,
                      ),
                      child: ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: state.length,
                        separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                        itemBuilder: (context, index) => GeneralContentHomeItem(generalContentModel: state[index]),
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
            BlocBuilder<RoleViewModel, List<RoleModel>?>(
              builder: (context, roleState) {
                return BlocBuilder<ParticipantViewModel, List<ParticipantModel>?>(
                  builder: (context, participantState) {
                    if (roleState == null ||
                        roleState.isEmpty ||
                        participantState == null ||
                        participantState.isEmpty) {
                      return const SizedBox.shrink();
                    }

                    return ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: const EdgeInsets.fromLTRB(
                        AppTheme.screenPadding,
                        0,
                        AppTheme.screenPadding,
                        AppTheme.widgetPadding,
                      ),
                      itemCount: roleState.length,
                      separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                      itemBuilder: (context, index) {
                        final role = roleState[index];
                        final spotlightParticipants = context.read<ParticipantViewModel>().getParticipantsByRole(
                          role.id ?? '',
                          spotlightOnly: true,
                        );

                        spotlightParticipants.clear();

                        return ContentArea(
                          padding: spotlightParticipants.isEmpty
                              ? const EdgeInsets.fromLTRB(
                                  AppTheme.widgetPaddingLarge,
                                  AppTheme.widgetPadding,
                                  0,
                                  AppTheme.widgetPadding,
                                )
                              : const EdgeInsets.fromLTRB(
                                  AppTheme.widgetPadding,
                                  AppTheme.widgetPadding,
                                  0,
                                  AppTheme.widgetPadding,
                                ),
                          onTap: () => context.go('${context.read<ConData>().conPath}/home/<USER>/${role.id}'),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(role.name ?? '', style: theme.textTheme.titleMedium),
                                    if (spotlightParticipants.isNotEmpty)
                                      Padding(
                                        padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                                        child: Wrap(
                                          runSpacing: AppTheme.widgetPaddingSmall,
                                          spacing: AppTheme.widgetPaddingSmall,
                                          children: [
                                            for (final spotlightParticipant in spotlightParticipants)
                                              ProfilePhotoView(profile: spotlightParticipant, size: 30),
                                          ],
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              const Padding(
                                padding: EdgeInsets.symmetric(horizontal: AppTheme.widgetPadding),
                                child: Icon(Icons.chevron_right),
                              ),
                            ],
                          ),
                        );
                      },
                    );
                  },
                );
              },
            ),
            AllDayEventsView(),
            BlocBuilder<ConViewModel, ConModel?>(
              builder: (context, state) {
                final conModel = state;
                if (conModel == null) {
                  return const SizedBox.shrink();
                }

                final widgets = [
                  if (context.watch<OrgViewModel>().state?.multipleConsPublished == true ||
                      context.watch<PermissionViewModel>().isPermitted(Rules.viewUnpublishedCons))
                    ListTile(
                      title: const Text('See all cons from this organization'),
                      titleTextStyle: theme.textTheme.titleSmall,
                      onTap: () => context.go('${context.read<ConData>().conPath}/home/<USER>'),
                      contentPadding: Theme.of(
                        context,
                      ).listTileTheme.contentPadding?.subtract(const EdgeInsets.only(right: AppTheme.widgetPadding)),
                      leading: const Icon(Icons.list),
                      trailing: const Icon(Icons.chevron_right),
                    ),
                  if (conModel.homepageUrl != null && conModel.homepageUrl!.isNotEmpty)
                    ListTile(
                      title: const Text('Official Website'),
                      titleTextStyle: theme.textTheme.titleSmall,
                      onTap: () => UrlHandler.open('https://${conModel.homepageUrl}'),
                      contentPadding: Theme.of(
                        context,
                      ).listTileTheme.contentPadding?.subtract(const EdgeInsets.only(right: AppTheme.widgetPadding)),
                      leading: const Icon(Icons.public),
                      trailing: const Icon(Icons.open_in_new),
                    ),
                  if (conModel.registrationUrl != null && conModel.registrationUrl!.isNotEmpty)
                    ListTile(
                      title: const Text('Register'),
                      titleTextStyle: theme.textTheme.titleSmall,
                      onTap: () => UrlHandler.open('https://${conModel.registrationUrl}'),
                      contentPadding: Theme.of(
                        context,
                      ).listTileTheme.contentPadding?.subtract(const EdgeInsets.only(right: AppTheme.widgetPadding)),
                      leading: const Icon(Icons.confirmation_num),
                      trailing: const Icon(Icons.open_in_new),
                    ),
                  if (conModel.socialMediaLinks != null && conModel.socialMediaLinks!.isNotEmpty)
                    Wrap(
                      alignment: WrapAlignment.center,
                      spacing: AppTheme.widgetPadding,
                      children: [
                        for (final socialMedia in conModel.socialMediaLinks!.keys)
                          IconButton(
                            tooltip: socialMedia.text,
                            icon: Icon(socialMedia.icon),
                            iconSize: 32,
                            onPressed: () =>
                                UrlHandler.open('${socialMedia.url}${conModel.socialMediaLinks![socialMedia]}'),
                          ),
                      ],
                    ),
                ];

                if (widgets.isEmpty) {
                  return const SizedBox.shrink();
                }

                return Padding(
                  padding: const EdgeInsets.fromLTRB(
                    AppTheme.screenPadding,
                    0,
                    AppTheme.screenPadding,
                    AppTheme.widgetPadding,
                  ),
                  child: ContentGroup(
                    title: '${conModel.name ?? 'Con'} Links',
                    padHorizontally: false,
                    child: ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: widgets.length,
                      itemBuilder: (context, index) => widgets[index],
                    ),
                  ),
                );
              },
            ),
            const AnnouncementView(isHighPriority: false),
            Padding(
              padding: const EdgeInsets.fromLTRB(
                AppTheme.screenPadding,
                0,
                AppTheme.screenPadding,
                AppTheme.widgetPadding,
              ),
              child: HostConButton(),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(
                AppTheme.screenPadding,
                0,
                AppTheme.screenPadding,
                AppTheme.widgetPadding,
              ),
              child: ShareButton(conData: context.read<ConData>(), text: context.read<ConViewModel>().state?.name),
            ),
          ],
        ),
      ),
    );
  }
}
