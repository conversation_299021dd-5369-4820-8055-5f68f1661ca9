import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:image_picker/image_picker.dart';
import 'package:venvi/constants/image_type.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/constants/social_media.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/models/profile_model.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/repositories/profile_repository.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_profile_state.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class EditProfileViewModel extends EditorScaffoldViewModel<EditProfileState> {
  final ConData? conData;
  final ProfileRepository _profileRepository;
  final ParticipantRepository _participantRepository;
  final ImageRepository _imageRepository;
  final String userId;

  EditProfileViewModel(
    EditProfileState initialState,
    this.conData,
    this._profileRepository,
    this._participantRepository,
    this._imageRepository,
    this.userId,
  ) : super(initialState) {
    final profile = initialState.profileModel;

    String? usernameErrorLock;

    final remainingUsernameTime = profile.lastUpdatedUsername
        ?.toDate()
        .add(InputConstants.lastUpdatedNameTimeout)
        .difference(DateTime.now());

    if (remainingUsernameTime != null && remainingUsernameTime.inSeconds > 0) {
      final daysRemaining = remainingUsernameTime.inDays;
      usernameErrorLock =
          'You can change your username in ${daysRemaining == 0
              ? 'less than a day'
              : daysRemaining == 1
              ? '1 day'
              : '$daysRemaining days'}';
    }

    emit(initialState.copyWith(profileModel: profile, usernameErrorLock: usernameErrorLock));
  }

  @override
  bool checkChanges(EditProfileState initialState, EditProfileState currentState) {
    return currentState.profileModel != initialState.profileModel ||
        currentState.conHeadline != initialState.conHeadline ||
        currentState.conBio != initialState.conBio ||
        currentState.profilePhotoOverrideFile != initialState.profilePhotoOverrideFile ||
        (initialState.useConHeadline == true && currentState.useConHeadline == false) ||
        (currentState.useConBio == true && currentState.useConBio == false);
  }

  @override
  Future<EditProfileState> applyChanges(EditProfileState initialState, EditProfileState state) async {
    ProfileModel model = state.profileModel;

    final errors = <String>[];
    if (model.username?.isNotEmpty != true) {
      errors.add('Username is required');
    }
    if (model.displayName?.isNotEmpty != true) {
      errors.add('Display name is required');
    }

    if (errors.isNotEmpty) {
      throw EditorScaffoldException(errors);
    }

    if (model.username != initialState.profileModel.username) {
      final usernameExists = await _profileRepository.checkIfUsernameExists(model.username!);
      if (usernameExists) {
        throw const EditorScaffoldException(['Username is already taken']);
      }

      model = model.copyWith(lastUpdatedUsername: Timestamp.now());
    }

    // Creates image model for profile photo
    late final ImageModel? profilePhotoUploadModel;
    if (state.profilePhotoOverrideFile != null && conData != null) {
      profilePhotoUploadModel = _imageRepository.createImageModel(conData!, ImageType.profilePhoto);
      model = model.copyWith(
        manualPhotoData: profilePhotoUploadModel,
        photoUrl: profilePhotoUploadModel.downloadUrl, // Update photoUrl to point to manual photo
      );
    } else {
      profilePhotoUploadModel = null;
    }

    final profileFuture = _profileRepository.updateProfile(userId, model);
    if (state.useConBio != null && state.useConHeadline != null && conData != null) {
      final success = await _participantRepository.updateParticipantConFields(
        conData!,
        userId,
        state.useConHeadline == true ? state.conHeadline : null,
        state.useConBio == true ? state.conBio : null,
      );
      if (!success) {
        throw const EditorScaffoldException(['Failed to update profile']);
      }
    }
    if (await profileFuture) {
      // Upload profile photo after profile is saved
      if (profilePhotoUploadModel != null && state.profilePhotoOverrideFile != null) {
        await _imageRepository.uploadImage(profilePhotoUploadModel, state.profilePhotoOverrideFile!);
      }
      return state.copyWith(profileModel: model);
    } else {
      throw const EditorScaffoldException(['Failed to update profile']);
    }
  }

  void setUsername(String username) {
    emit(state.copyWith(profileModel: state.profileModel.copyWith(username: username.trim()), usernameErrorLock: null));
  }

  void setDisplayName(String displayName) {
    emit(state.copyWith(profileModel: state.profileModel.copyWith(displayName: displayName.trim())));
  }

  void setHeadline(String headline) {
    emit(state.copyWith(profileModel: state.profileModel.copyWith(headline: headline.trim())));
  }

  void setConHeadline(String conHeadline) {
    emit(state.copyWith(conHeadline: conHeadline.trim()));
  }

  void toggleUseConHeadline() {
    emit(state.copyWith(useConHeadline: !(state.useConHeadline ?? false)));
  }

  void setBio(String bio) {
    emit(state.copyWith(profileModel: state.profileModel.copyWith(bio: bio.trim())));
  }

  void setConBio(String conBio) {
    emit(state.copyWith(conBio: conBio.trim()));
  }

  void toggleUseConBio() {
    emit(state.copyWith(useConBio: !(state.useConBio ?? false)));
  }

  void setSocialMediaLink(SocialMedia socialMedia, String link) {
    final socialMediaMap = Map<SocialMedia, String>.from(state.profileModel.socialMediaLinks ?? {});
    socialMediaMap[socialMedia] = link.trim();
    final model = state.profileModel.copyWith(socialMediaLinks: socialMediaMap);
    emit(state.copyWith(profileModel: model));
  }

  void removeSocialMediaLink(SocialMedia socialMedia) {
    final socialMediaMap = Map<SocialMedia, String>.from(state.profileModel.socialMediaLinks ?? {});
    socialMediaMap.remove(socialMedia);
    final model = state.profileModel.copyWith(socialMediaLinks: socialMediaMap);
    emit(state.copyWith(profileModel: model));
  }

  void setProfilePhotoFile(XFile image) {
    final newState = state.copyWith(
      profileModel: state.profileModel.copyWith(manualPhotoData: null),
      profilePhotoOverrideFile: image,
    );
    emit(newState);
  }

  void removeProfilePhoto() {
    final newState = state.copyWith(
      profileModel: state.profileModel.copyWith(
        manualPhotoData: null,
        photoUrl: state.profileModel.authPhotoUrl, // Revert to auth photo
      ),
      profilePhotoOverrideFile: null,
    );
    emit(newState);
  }
}
