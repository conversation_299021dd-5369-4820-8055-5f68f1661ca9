import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/constants/social_media.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/auth_view_model.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/models/base_profile.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/profile_model.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/repositories/profile_repository.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_profile_state.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_profile_view_model.dart';
import 'package:venvi/screen_dialogs/edit_profile/profile_stream_cubit.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/editor_scaffold.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/input_error_message.dart';
import 'package:venvi/widgets/surface_input_field.dart';
import 'package:venvi/widgets/url_text_field.dart';

class EditProfileScreen extends HookWidget {
  const EditProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final scrollController = useScrollController();
    final displayNameController = useTextEditingController();
    final usernameController = useTextEditingController();
    final conHeadlineController = useTextEditingController();
    final headlineController = useTextEditingController();
    final conBioController = useTextEditingController();
    final bioController = useTextEditingController();
    final Map<SocialMedia, TextEditingController> socialMediaControllers = Map.fromEntries(
      SocialMedia.values.map((socialMedia) => MapEntry(socialMedia, useTextEditingController())),
    );

    final authState = context.read<AuthViewModel>().state;
    final userId = authState?.isAnonymous == false ? authState?.uid : null;
    if (userId == null) {
      return const Scaffold(
        appBar: GlassAppBar(title: Text('Edit Profile'), isFullScreenDialog: true),
        body: Center(
          child: ContentArea(
            fillWidth: false,
            child: Text('You must be logged in to edit your profile', textAlign: TextAlign.center),
          ),
        ),
      );
    }

    late final ParticipantViewModel? participantViewModel;
    try {
      participantViewModel = context.read<ParticipantViewModel>();
    } catch (e) {
      participantViewModel = null;
    }
    return BlocProvider(
      create: (context) => ProfileStreamCubit(context.read<ProfileRepository>(), participantViewModel, userId),
      child: BlocBuilder<ProfileStreamCubit, BaseProfile?>(
        builder: (context, state) {
          if (state == null) {
            return Scaffold(
              appBar: GlassAppBar(title: Text('Edit Profile'), isFullScreenDialog: true),
              body: Center(child: CircularProgressIndicator(color: Theme.of(context).colorScheme.onPrimaryContainer)),
            );
          }

          return EditorScaffold<EditProfileViewModel, EditProfileState, ProfileStreamCubit, BaseProfile?>(
            isFullScreenDialog: true,
            externalCubit: context.read<ProfileStreamCubit>(),
            buildState: (externalCubitState) {
              if (externalCubitState == null) {
                return null;
              } else if (externalCubitState is ParticipantModel) {
                return EditProfileState(
                  profileModel: ProfileModel.fromParticipantModel(externalCubitState),
                  conHeadline: externalCubitState.conHeadline,
                  useConHeadline: externalCubitState.conHeadline != null,
                  conBio: externalCubitState.conBio,
                  useConBio: externalCubitState.conBio != null,
                );
              } else if (externalCubitState is ProfileModel) {
                return EditProfileState(
                  profileModel: externalCubitState,
                  conHeadline: null,
                  useConHeadline: null,
                  conBio: null,
                  useConBio: null,
                );
              } else {
                return null;
              }
            },
            initViewModel: (state) {
              displayNameController.text = state.profileModel.displayName ?? '';
              usernameController.text = state.profileModel.username ?? '';
              headlineController.text = state.profileModel.headline ?? '';
              conHeadlineController.text = state.conHeadline ?? '';
              bioController.text = state.profileModel.bio ?? '';
              conBioController.text = state.conBio ?? '';
              socialMediaControllers.forEach((socialMedia, controller) {
                controller.text = state.profileModel.socialMediaLinks?[socialMedia] ?? '';
              });

              late final ConData? conData;
              try {
                conData = context.read<ConData>();
              } catch (e) {
                conData = null;
              }
              return EditProfileViewModel(
                state,
                conData,
                context.read<ProfileRepository>(),
                context.read<ParticipantRepository>(),
                userId,
              );
            },
            title: const Text('Edit Profile'),
            successMessage: 'Profile Updated',
            buildContent: (context, viewModel, state) {
              late final String? conName;
              try {
                conName = context.read<ConViewModel>().state?.name;
              } catch (e) {
                conName = null;
              }
              return ListView(
                controller: scrollController,
                padding: const EdgeInsets.all(AppTheme.screenPadding),
                children: [
                  ContentGroup(
                    title: 'Display Name',
                    child: SurfaceInputField(
                      child: TextField(
                        controller: displayNameController,
                        onChanged: (value) => viewModel.setDisplayName(value),
                        textInputAction: TextInputAction.next,
                        textCapitalization: TextCapitalization.words,
                        textAlign: TextAlign.center,
                        maxLength: InputConstants.maxProfileDisplayNameLength,
                        decoration: const InputDecoration(hintText: 'Display Name', counterText: ''),
                      ),
                    ),
                  ),
                  const SizedBox(height: AppTheme.widgetPadding),
                  ContentGroup(
                    title: 'Username',
                    children: [
                      SurfaceInputField(
                        child: TextField(
                          controller: usernameController,
                          enabled: state.usernameErrorLock == null,
                          onChanged: (value) => viewModel.setUsername(value),
                          textInputAction: TextInputAction.next,
                          textCapitalization: TextCapitalization.words,
                          textAlign: TextAlign.center,
                          maxLength: InputConstants.maxProfileUsernameLength,
                          decoration: const InputDecoration(counterText: ''),
                          inputFormatters: [FilteringTextInputFormatter.deny(InputConstants.usernameRegEx)],
                        ),
                      ),
                      if (state.usernameErrorLock != null) InputErrorMessage(message: state.usernameErrorLock!),
                    ],
                  ),
                  if (state.useConHeadline != null)
                    Padding(
                      padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                      child: ContentArea(
                        padding: EdgeInsets.zero,
                        child: SwitchListTile(
                          title: Text('Use ${conName ?? 'Con'} Specific Headline'),
                          value: state.useConHeadline!,
                          onChanged: (value) => viewModel.toggleUseConHeadline(),
                        ),
                      ),
                    ),
                  if (state.useConHeadline == true)
                    Padding(
                      padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                      child: ContentGroup(
                        title: '${conName ?? 'Con'} Headline',
                        child: SurfaceInputField(
                          child: TextField(
                            controller: conHeadlineController,
                            onChanged: (value) => viewModel.setConHeadline(value),
                            textInputAction: TextInputAction.newline,
                            maxLines: 5,
                            textCapitalization: TextCapitalization.sentences,
                            maxLength: InputConstants.maxProfileHeadlineLength,
                            decoration: const InputDecoration(
                              contentPadding: EdgeInsets.all(AppTheme.widgetPadding),
                              counterText: '',
                            ),
                          ),
                        ),
                      ),
                    ),
                  const SizedBox(height: AppTheme.widgetPadding),
                  ContentGroup(
                    title: 'Headline',
                    child: SurfaceInputField(
                      child: TextField(
                        controller: headlineController,
                        onChanged: (value) => viewModel.setHeadline(value),
                        textInputAction: TextInputAction.newline,
                        maxLines: 5,
                        textCapitalization: TextCapitalization.sentences,
                        maxLength: InputConstants.maxProfileHeadlineLength,
                        decoration: const InputDecoration(
                          contentPadding: EdgeInsets.all(AppTheme.widgetPadding),
                          counterText: '',
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: AppTheme.widgetPadding),
                  if (state.useConBio != null)
                    Padding(
                      padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                      child: ContentArea(
                        padding: EdgeInsets.zero,
                        child: SwitchListTile(
                          title: Text('Use ${conName ?? 'Con'} Specific Bio'),
                          value: state.useConBio!,
                          onChanged: (value) => viewModel.toggleUseConBio(),
                        ),
                      ),
                    ),
                  if (state.useConBio == true)
                    Padding(
                      padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                      child: ContentGroup(
                        title: '${conName ?? 'Con'} Bio',
                        child: SurfaceInputField(
                          child: TextField(
                            controller: conBioController,
                            onChanged: (value) => viewModel.setConBio(value),
                            textInputAction: TextInputAction.newline,
                            maxLines: 10,
                            textCapitalization: TextCapitalization.sentences,
                            maxLength: InputConstants.maxProfileBioLength,
                            decoration: const InputDecoration(
                              contentPadding: EdgeInsets.all(AppTheme.widgetPadding),
                              counterText: '',
                            ),
                          ),
                        ),
                      ),
                    ),
                  const SizedBox(height: AppTheme.widgetPadding),
                  ContentGroup(
                    title: 'Bio',
                    child: SurfaceInputField(
                      child: TextField(
                        controller: bioController,
                        onChanged: (value) => viewModel.setBio(value),
                        textInputAction: TextInputAction.newline,
                        maxLines: 10,
                        textCapitalization: TextCapitalization.sentences,
                        maxLength: InputConstants.maxProfileBioLength,
                        decoration: const InputDecoration(
                          contentPadding: EdgeInsets.all(AppTheme.widgetPadding),
                          counterText: '',
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: AppTheme.widgetPadding),
                  ContentGroup(
                    title: 'Social Media Links',
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Wrap(
                          alignment: WrapAlignment.center,
                          spacing: AppTheme.widgetPaddingSmall,
                          children: List.generate(SocialMedia.values.length, (index) {
                            final socialMedia = SocialMedia.values[index];
                            return ChoiceChip(
                              label: Icon(socialMedia.icon, color: Theme.of(context).colorScheme.onPrimary),
                              selected: state.profileModel.socialMediaLinks?.containsKey(socialMedia) ?? false,
                              onSelected: (value) {
                                if (value) {
                                  viewModel.setSocialMediaLink(socialMedia, '');
                                  WidgetsBinding.instance.addPostFrameCallback((_) {
                                    scrollController.animateTo(
                                      scrollController.position.maxScrollExtent,
                                      duration: const Duration(milliseconds: 500),
                                      curve: Curves.easeInOut,
                                    );
                                  });
                                } else {
                                  viewModel.removeSocialMediaLink(socialMedia);
                                }
                              },
                            );
                          }),
                        ),
                        if (state.profileModel.socialMediaLinks?.isNotEmpty == true)
                          Padding(
                            padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                            child: ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: state.profileModel.socialMediaLinks?.length ?? 0,
                              separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                              itemBuilder: (context, index) {
                                final socialMedia = state.profileModel.socialMediaLinks!.keys.elementAt(index);
                                return UrlTextField(
                                  controller: socialMediaControllers[socialMedia],
                                  onChanged: (value) => viewModel.setSocialMediaLink(socialMedia, value),
                                  label: socialMedia.text,
                                  prefix: socialMedia.url,
                                  icon: socialMedia.icon,
                                  value: state.profileModel.socialMediaLinks![socialMedia],
                                );
                              },
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }
}
